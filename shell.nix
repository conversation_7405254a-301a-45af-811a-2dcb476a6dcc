{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build tools
    xmake
    gcc
    pkg-config
    cmake
    libiconv
    
    # Raylib and dependencies
    raylib
    
    # Audio libraries
    alsa-lib
    pulseaudio
    
    # Graphics libraries
    libGL
    libGLU
    
    # X11 libraries
    xorg.libX11
    xorg.libXrandr
    xorg.libXinerama
    xorg.libXi
    xorg.libXxf86vm
    xorg.libXcursor
    
    # Wayland libraries (optional)
    wayland
    wayland-protocols
    libxkbcommon
    
    # Development tools
    gdb
    valgrind
    strace
    ltrace
    
    # Code analysis and formatting
    clang-tools
    cppcheck
    
    # Version control
    git
    
    # Documentation
    doxygen
  ];

  shellHook = ''
    echo "==================================="
    echo "Controller Synth Development Shell"
    echo "==================================="
    echo ""
    echo "Available commands:"
    echo "  xmake config --mode=debug    # Configure for debug build"
    echo "  xmake config --mode=release  # Configure for release build"
    echo "  xmake build                  # Build the project"
    echo "  xmake run                    # Run the application"
    echo "  xmake clean                  # Clean build files"
    echo ""
    echo "Debug tools:"
    echo "  gdb ./bin/controller-synth   # Debug with GDB"
    echo "  valgrind ./bin/controller-synth  # Memory debugging"
    echo ""
    echo "Make sure you have a gamepad connected for testing!"
    echo ""
    
    # Set up environment variables for proper library linking
    export PKG_CONFIG_PATH="${pkgs.lib.makeSearchPath "lib/pkgconfig" (with pkgs; [
      raylib
      alsa-lib
      libGL
      xorg.libX11
      xorg.libXrandr
      xorg.libXinerama
      xorg.libXi
      xorg.libXxf86vm
      xorg.libXcursor
      wayland
      libxkbcommon
      libiconv
    ])}:$PKG_CONFIG_PATH"
    
    export LD_LIBRARY_PATH="${pkgs.lib.makeLibraryPath (with pkgs; [
      raylib
      alsa-lib
      pulseaudio
      libGL
      libGLU
      xorg.libX11
      xorg.libXrandr
      xorg.libXinerama
      xorg.libXi
      xorg.libXxf86vm
      xorg.libXcursor
      wayland
      libxkbcommon
      glibc
      libiconv
    ])}:$LD_LIBRARY_PATH"
    
    # Ensure xmake can find dependencies
    export XMAKE_ROOT=$HOME/.xmake
    mkdir -p $XMAKE_ROOT
    
    # Set up audio permissions (might be needed for some systems)
    export PULSE_RUNTIME_PATH=/run/user/$(id -u)/pulse
  '';
}
