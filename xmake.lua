-- Project settings
set_project("controller-synth")
set_version("1.0.0")
set_xmakever("2.5.0")

-- Add dependency packages
add_requires("raylib", {configs = {shared = false, opengl_version = "33"}})

target("controller-synth")
    set_kind("binary")
    set_languages("c99")
    add_rules("utils.bin2c", {extensions = {".mp3"}})

    -- File settings
    add_files("src/**.c")
    -- add_includedirs("include")
    add_includedirs("src")
    add_files("resources/*.mp3")
    add_defines(
        "PLATFORM_DESKTOP",
        "GRAPHICS_API_OPENGL_33",
        "_GNU_SOURCE",
        "_DEFAULT_SOURCE",
        "RAYGUI_IMPLEMENTATION"
    )

    -- Warning and optimization settings
    add_cflags("-Wall", "-Wextra", "-O2")
    add_cxxflags("-Wall", "-Wextra", "-O2")

    -- Settings for GDB debugging
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_cflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_cxxflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_ldflags("-g3")
        add_defines("DEBUG", "DEBUG_GL", "GL_DEBUG")

        -- Enable OpenGL debugging
        if is_os("linux") then
            add_defines("GL_GLEXT_PROTOTYPES")
            add_cflags("-rdynamic") -- Improve symbol resolution for backtraces
            add_ldflags("-rdynamic")
        end
    end

    -- Platform specific settings
    if is_os("linux") then
        -- Basic system libraries (excluding iconv for NixOS compatibility)
        add_syslinks("dl", "m", "pthread", "rt")
        -- X11 libraries
        add_syslinks("X11", "Xrandr", "Xinerama", "Xi", "Xxf86vm", "Xcursor")
        -- Wayland libraries (optional, for Wayland support)
        add_syslinks("wayland-client", "wayland-cursor", "wayland-egl", "xkbcommon")
        -- OpenGL libraries
        add_syslinks("GL")
        -- Audio libraries
        add_syslinks("asound")
        -- Input device libraries for gamepad support
        add_syslinks("udev", "evdev")
        add_defines("PLATFORM_DESKTOP")
    elseif is_os("macosx") then
        add_frameworks("OpenGL", "Cocoa", "IOKit", "CoreVideo", "CoreAudio")
    end

    -- Windows cross-compilation settings
    if is_plat("windows") then
        set_toolchains("mingw")
        -- Force ANSI API
        add_defines("_WIN32", "WIN32")
        -- Basic system libraries
        add_syslinks("kernel32", "user32", "gdi32", "winmm", "shell32", "comctl32", "ole32")
        -- Windows version definitions
        add_defines("WINVER=0x0601", "_WIN32_WINNT=0x0601")
        -- Static linking flags
        add_ldflags("-static-libgcc", "-static-libstdc++", "-Wl,-Bstatic", "-lpthread", "-Wl,-Bdynamic", {force = true})
        -- libiconv dependencies
        add_requires("libiconv")
        add_packages("libiconv")
    else
        -- For non-Windows platforms, iconv is usually part of glibc (especially on NixOS)
        -- Only add iconv link if not in NixOS environment
        -- add_links("iconv")  -- Commented out for NixOS compatibility
    end

	if is_plat("windows", "mingw") then
		add_syslinks("Comdlg32")
	end

    -- Set warning flags
    if is_plat("windows") then
        if is_mode("release") then
            -- Suppress warnings for release builds
            add_cflags("-Wno-cast-function-type", "-Wno-unused-variable", {force = true})
        end
    end

    -- Raylib dependencies
    add_packages("raylib")

    -- Output directory
    set_targetdir("bin/")
