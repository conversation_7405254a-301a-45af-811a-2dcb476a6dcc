#include "raylib.h"
#include <stdio.h>
#include <math.h> // Include for powf
#include <stdbool.h> // Include for bool type
#include <string.h> // Include for strstr
#include <time.h> // Include for GetTime()

#define SCREEN_WIDTH 800
#define SCREEN_HEIGHT 550 // 鍵盤のスペースを確保するために高さを増加

// Pitch constants
#define PITCH_SEMITONE_DOWN (powf(2.0f, -1.0f/12.0f)) // 2^(-1/12) ~ 0.94387f
#define PITCH_SEMITONE_UP   (powf(2.0f, 1.0f/12.0f))  // 2^(1/12) ~ 1.05946f
#define PITCH_OCTAVE_UP 2.0f

// Controller Overlay Positions (Adjusted based on image)
#define DPAD_CLUSTER_X 100 // Base X for D-Pad and LB
#define FACE_CLUSTER_X 300 // Base X for Face Buttons and RB
#define BUTTON_ROW_Y 150   // Base Y for D-Pad and Face Buttons
#define SHOULDER_ROW_Y 100 // Base Y for LB and RB

#define OVERLAY_SCALE 1.0f // Reset scale, adjust positions directly
#define INDICATOR_RADIUS 10.0f
#define BUTTON_RADIUS 15.0f // Slightly larger buttons
#define LABEL_FONT_SIZE 10
#define SHOULDER_BTN_WIDTH 60
#define SHOULDER_BTN_HEIGHT 20
#define DPAD_ARM_LENGTH 25.0f
#define DPAD_ARM_WIDTH 18.0f
#define FACE_BTN_OFFSET 25.0f // Offset for X/B and Y/A from center

// --- Remove Bar constants ---
// #define BAR_Y (SCREEN_HEIGHT - 20)
// #define BAR_HEIGHT 10
// #define BAR_SPEED 0.5f
// #define BAR_COLOR BLUE

// --- Add Button Identifiers ---
typedef enum {
    BTN_NONE = 0, // Must be 0
    BTN_A,
    BTN_B,
    BTN_X,
    BTN_Y,
    BTN_DPAD_UP,
    BTN_DPAD_DOWN,
    BTN_DPAD_LEFT,
    BTN_DPAD_RIGHT,
    BTN_LB,
    BTN_RB,
    NUM_BUTTON_IDS // Keep track of the number of buttons (including BTN_NONE)
} ButtonId;

// --- Add Bar Properties per Button ---
#define BAR_HEIGHT 15
#define BAR_SPEED 1.0f // Slightly faster speed
#define MAX_ACTIVE_BARS 1000 // Allow multiple bars simultaneously (more than buttons)

const Color BUTTON_BAR_COLORS[NUM_BUTTON_IDS] = {
    BLANK,      // BTN_NONE
    LIME,       // BTN_A
    PINK,       // BTN_B
    SKYBLUE,    // BTN_X
    ORANGE,     // BTN_Y
    SKYBLUE,    // BTN_DPAD_UP
    SKYBLUE,    // BTN_DPAD_DOWN
    SKYBLUE,    // BTN_DPAD_LEFT
    SKYBLUE,    // BTN_DPAD_RIGHT
    RED,        // BTN_LB
    RED         // BTN_RB
};

const int BUTTON_BAR_Y[NUM_BUTTON_IDS] = {
    0, // BTN_NONE - Not used
    SCREEN_HEIGHT - BAR_HEIGHT * 8, // BTN_A (Bottom-most bar)
    SCREEN_HEIGHT - BAR_HEIGHT * 9,  // BTN_B
    SCREEN_HEIGHT - BAR_HEIGHT * 7,  // BTN_X
    SCREEN_HEIGHT - BAR_HEIGHT * 10,  // BTN_Y
    SCREEN_HEIGHT - BAR_HEIGHT * 5,  // BTN_DPAD_UP
    SCREEN_HEIGHT - BAR_HEIGHT * 3,  // BTN_DPAD_DOWN
    SCREEN_HEIGHT - BAR_HEIGHT * 4,  // BTN_DPAD_LEFT
    SCREEN_HEIGHT - BAR_HEIGHT * 6,  // BTN_DPAD_RIGHT
    SCREEN_HEIGHT - BAR_HEIGHT * 2,  // BTN_LB
    SCREEN_HEIGHT - BAR_HEIGHT * 1   // BTN_RB (Top-most bar)
};

// --- Bar State ---
typedef enum {
    BAR_IDLE,
    BAR_FLOWING,
    BAR_FADING_OUT
} BarState;

// --- Structure to hold state for each active bar instance ---
typedef struct {
    BarState state;
    ButtonId button_id; // Which button triggered this bar
    float progress; // Increases while flowing (not wrapped)
    float visual_start_x; // For fade out animation
    float visual_end_x;   // For fade out animation
    // Color and Y are now looked up via button_id
} BarInfo;

// --- Button Positions (Recalculated based on image) ---
// Face Buttons
const Vector2 POS_Y = {FACE_CLUSTER_X, BUTTON_ROW_Y - FACE_BTN_OFFSET}; // Top
const Vector2 POS_X = {FACE_CLUSTER_X - FACE_BTN_OFFSET, BUTTON_ROW_Y}; // Left
const Vector2 POS_B = {FACE_CLUSTER_X + FACE_BTN_OFFSET, BUTTON_ROW_Y}; // Right
const Vector2 POS_A = {FACE_CLUSTER_X, BUTTON_ROW_Y + FACE_BTN_OFFSET}; // Bottom
// D-Pad (Center for drawing cross)
const Vector2 POS_DPAD_CENTER = {DPAD_CLUSTER_X, BUTTON_ROW_Y};
// D-Pad Indicator Positions (Relative to new center) - Keep for indicator logic
const Vector2 POS_DPAD_UP = {POS_DPAD_CENTER.x, POS_DPAD_CENTER.y - DPAD_ARM_LENGTH};
const Vector2 POS_DPAD_DOWN = {POS_DPAD_CENTER.x, POS_DPAD_CENTER.y + DPAD_ARM_LENGTH};
const Vector2 POS_DPAD_LEFT = {POS_DPAD_CENTER.x - DPAD_ARM_LENGTH, POS_DPAD_CENTER.y};
const Vector2 POS_DPAD_RIGHT = {POS_DPAD_CENTER.x + DPAD_ARM_LENGTH, POS_DPAD_CENTER.y};
// Shoulder Buttons
const Vector2 POS_LB = {DPAD_CLUSTER_X, SHOULDER_ROW_Y};
const Vector2 POS_RB = {FACE_CLUSTER_X, SHOULDER_ROW_Y};
const Vector2 SHOULDER_BTN_SIZE = {SHOULDER_BTN_WIDTH, SHOULDER_BTN_HEIGHT};

// --- CPS Graph Constants and Variables ---
#define MAX_TIMESTAMPS 512       // Store last 512 button press timestamps
#define CPS_WINDOW_SECONDS 1.0f  // Calculate CPS over the last 1 second
#define GRAPH_HISTORY_LENGTH 64 // Store last 300 CPS values for the graph (Increased history)
#define GRAPH_X 450              // Graph position X (Adjusted)
#define GRAPH_Y 25               // Graph position Y (Adjusted)
#define GRAPH_WIDTH 320          // Graph width (Increased)
#define GRAPH_HEIGHT 150         // Graph height (Increased)
// #define MAX_EXPECTED_CPS 20.0f   // Removed fixed max CPS
#define GRAPH_LINE_THICKNESS 2.5f // Thickness for the graph line
#define MIN_GRAPH_RANGE 5.0f      // Minimum range for Y-axis scaling to avoid flatness
#define GRAPH_SCALE_SMOOTH_FACTOR 8.0f // Controls how quickly the scale adapts (higher = faster)
#define GRAPH_UPDATE_INTERVAL (1.0f / 60.0f) // Update graph history at ~60Hz rate

static double press_timestamps[MAX_TIMESTAMPS] = {0};
static int timestamp_index = 0;
static int timestamp_count = 0; // How many timestamps are currently stored (up to MAX_TIMESTAMPS)

static float cps_history[GRAPH_HISTORY_LENGTH] = {0};
static int cps_history_index = 0;
static float current_cps = 0.0f;
static float graph_min_cps = 0.0f; // Current interpolated min CPS for drawing
static float graph_max_cps = MIN_GRAPH_RANGE; // Current interpolated max CPS for drawing
static float target_graph_min_cps = 0.0f; // Target min CPS based on history
static float target_graph_max_cps = MIN_GRAPH_RANGE; // Target max CPS based on history
static float graph_update_timer = 0.0f; // Timer for graph history update
// --- End CPS Graph Constants and Variables ---

// --- Piano Keyboard Constants and Variables ---
#define WHITE_KEY_WIDTH 20
#define WHITE_KEY_HEIGHT 100
#define BLACK_KEY_WIDTH 12
#define BLACK_KEY_HEIGHT 60
#define KEYBOARD_Y (SCREEN_HEIGHT - WHITE_KEY_HEIGHT - 20) // 鍵盤の位置（画面下部）
#define KEYBOARD_X ((SCREEN_WIDTH - (WHITE_KEY_WIDTH * 14)) / 2) // 鍵盤の位置（画面中央）
#define NUM_KEYS 24 // 2オクターブ分（白鍵14個、黒鍵10個）

// 鍵盤の種類
typedef enum {
    KEY_WHITE,
    KEY_BLACK
} KeyType;

// 鍵盤の情報
typedef struct {
    Rectangle rect;  // 鍵盤の矩形
    KeyType type;    // 鍵盤の種類（白鍵/黒鍵）
    bool isPressed;  // 押されているかどうか
    int noteIndex;   // 音符のインデックス（0-23）
    ButtonId buttonId; // 対応するボタンID
} PianoKey;

static PianoKey keyboard[NUM_KEYS]; // 鍵盤の配列
static bool keyboardInitialized = false; // 鍵盤が初期化されたかどうか
static bool isPitchSemitoneUp = false; // 半音上げフラグ
static bool isPitchOctaveUp = false; // オクターブ上げフラグ

static unsigned char a4_data[] = {
    #include "a4.mp3.h"
};

static unsigned char b4_data[] = {
    #include "b4.mp3.h"
};

static unsigned char c4_data[] = {
    #include "c4.mp3.h"
};

static unsigned char c5_data[] = {
    #include "c5.mp3.h"
};

static unsigned char d4_data[] = {
    #include "d4.mp3.h"
};

static unsigned char e4_data[] = {
    #include "e4.mp3.h"
};

static unsigned char f4_data[] = {
    #include "f4.mp3.h"
};

static unsigned char g4_data[] = {
    #include "g4.mp3.h"
};

// Define sound data sizes
static unsigned int a4_data_size = sizeof(a4_data);
static unsigned int b4_data_size = sizeof(b4_data);
static unsigned int c4_data_size = sizeof(c4_data);
static unsigned int c5_data_size = sizeof(c5_data);
static unsigned int d4_data_size = sizeof(d4_data);
static unsigned int e4_data_size = sizeof(e4_data);
static unsigned int f4_data_size = sizeof(f4_data);
static unsigned int g4_data_size = sizeof(g4_data);

// Helper function to check if a gamepad is a real controller (not touchpad, etc.)
bool IsRealGamepad(int gamepad) {
    if (!IsGamepadAvailable(gamepad)) return false;

    const char* name = GetGamepadName(gamepad);
    if (!name) return false;

    // Filter out touchpads and other non-controller devices
    if (strstr(name, "Touchpad") || strstr(name, "touchpad") ||
        strstr(name, "Mouse") || strstr(name, "mouse") ||
        strstr(name, "Keyboard") || strstr(name, "keyboard")) {
        return false;
    }

    return true;
}

// Helper function to find the first real gamepad
int FindFirstRealGamepad() {
    for (int i = 0; i < 4; i++) {
        if (IsRealGamepad(i)) {
            return i;
        }
    }
    return -1; // No real gamepad found
}

// Helper function to map ButtonId to GamepadButton
int GetGamepadButton(ButtonId id) {
    switch (id) {
        case BTN_A: return GAMEPAD_BUTTON_RIGHT_FACE_DOWN;
        case BTN_B: return GAMEPAD_BUTTON_RIGHT_FACE_RIGHT;
        case BTN_X: return GAMEPAD_BUTTON_RIGHT_FACE_LEFT;
        case BTN_Y: return GAMEPAD_BUTTON_RIGHT_FACE_UP;
        case BTN_DPAD_UP: return GAMEPAD_BUTTON_LEFT_FACE_UP;
        case BTN_DPAD_DOWN: return GAMEPAD_BUTTON_LEFT_FACE_DOWN;
        case BTN_DPAD_LEFT: return GAMEPAD_BUTTON_LEFT_FACE_LEFT;
        case BTN_DPAD_RIGHT: return GAMEPAD_BUTTON_LEFT_FACE_RIGHT;
        case BTN_LB: return GAMEPAD_BUTTON_LEFT_TRIGGER_1;
        case BTN_RB: return GAMEPAD_BUTTON_RIGHT_TRIGGER_1;
        default: return -1; // Invalid button
    }
}

// --- CPS Calculation Function ---
// Calculates current CPS and determines target graph scale based on history
void UpdateCPS(void) {
    double current_time = GetTime();
    double window_start_time = current_time - CPS_WINDOW_SECONDS;
    int presses_in_window = 0;

    // Count presses within the time window using the circular buffer
    for (int i = 0; i < timestamp_count; i++) {
        // Calculate the actual index in the circular buffer
        int current_index = (timestamp_index - 1 - i + MAX_TIMESTAMPS) % MAX_TIMESTAMPS;
        if (press_timestamps[current_index] >= window_start_time) {
            presses_in_window++;
        } else {
            // Since timestamps are ordered, we can stop counting early
            break;
        }
    }

    current_cps = (float)presses_in_window / CPS_WINDOW_SECONDS;

    // --- Calculate Target Min/Max CPS for Graph Scaling ---
    // Find min and max in the *entire* history buffer for dynamic scaling
    float min_val = cps_history[0];
    float max_val = cps_history[0];
    // Only consider actual data points if history isn't full yet
    int count = 0;
    for(int i = 0; i < GRAPH_HISTORY_LENGTH; ++i) {
        // A simple check to see if the buffer has wrapped around or filled
        // This assumes initial values are 0. A more robust check might be needed
        // if 0 CPS is a frequent valid value early on.
        if (cps_history[i] != 0.0f || i == cps_history_index -1 || i == 0) { // Check if value is likely set
             if (count == 0) { // First valid value
                 min_val = cps_history[i];
                 max_val = cps_history[i];
             } else {
                 if (cps_history[i] < min_val) min_val = cps_history[i];
                 if (cps_history[i] > max_val) max_val = cps_history[i];
             }
             count++;
        }
    }
     // If no valid data points found yet, keep default range
    if (count == 0) {
        min_val = 0.0f;
        max_val = MIN_GRAPH_RANGE;
    }


    // Apply a minimum range to avoid a flat graph when CPS is constant
    if (max_val - min_val < MIN_GRAPH_RANGE) {
        float mid = (max_val + min_val) / 2.0f;
        target_graph_min_cps = fmaxf(0.0f, mid - MIN_GRAPH_RANGE / 2.0f); // Ensure min is not negative
        target_graph_max_cps = target_graph_min_cps + MIN_GRAPH_RANGE;
    } else {
        // Add a small buffer to min/max to prevent line touching edges immediately
        float buffer = (max_val - min_val) * 0.05f; // 5% buffer
        target_graph_min_cps = fmaxf(0.0f, min_val - buffer); // Min shouldn't go below 0
        target_graph_max_cps = max_val + buffer;
    }
    // --- End Calculate Target Min/Max CPS ---

    // NOTE: Storing into cps_history is moved to the main loop timer
}

// --- Piano Keyboard Functions ---
// 関数プロトタイプ宣言
void UpdateKeyboardMapping(bool semitoneUp, bool octaveUp);
// 鍵盤の初期化
void InitPianoKeyboard(void) {
    if (keyboardInitialized) return;

    // 白鍵と黒鍵の配置パターン（1オクターブ分）
    // 0: 白鍵, 1: 黒鍵
    const int keyPattern[12] = {0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0}; // C, C#, D, D#, E, F, F#, G, G#, A, A#, B

    int whiteKeyCount = 0;
    int blackKeyCount = 0;

    // 2オクターブ分の鍵盤を初期化
    for (int i = 0; i < NUM_KEYS; i++) {
        int octaveIndex = i % 12; // 1オクターブ内でのインデックス
        KeyType keyType = (keyPattern[octaveIndex] == 0) ? KEY_WHITE : KEY_BLACK;

        if (keyType == KEY_WHITE) {
            // 白鍵の位置を設定
            keyboard[i].rect.x = KEYBOARD_X + (whiteKeyCount * WHITE_KEY_WIDTH);
            keyboard[i].rect.y = KEYBOARD_Y;
            keyboard[i].rect.width = WHITE_KEY_WIDTH;
            keyboard[i].rect.height = WHITE_KEY_HEIGHT;
            whiteKeyCount++;
        } else {
            // 黒鍵の位置を設定（前の白鍵の右側に配置）
            // 黒鍵のインデックスに基づいて位置を調整
            float offset = 0;
            switch (octaveIndex) {
                case 1: offset = WHITE_KEY_WIDTH * 0.7f; break; // C#
                case 3: offset = WHITE_KEY_WIDTH * 0.7f; break; // D#
                case 6: offset = WHITE_KEY_WIDTH * 0.7f; break; // F#
                case 8: offset = WHITE_KEY_WIDTH * 0.7f; break; // G#
                case 10: offset = WHITE_KEY_WIDTH * 0.7f; break; // A#
            }

            keyboard[i].rect.x = KEYBOARD_X + (whiteKeyCount - 1) * WHITE_KEY_WIDTH + offset;
            keyboard[i].rect.y = KEYBOARD_Y;
            keyboard[i].rect.width = BLACK_KEY_WIDTH;
            keyboard[i].rect.height = BLACK_KEY_HEIGHT;
            blackKeyCount++;
        }

        keyboard[i].type = keyType;
        keyboard[i].isPressed = false;
        keyboard[i].noteIndex = i;
        keyboard[i].buttonId = BTN_NONE; // 初期状態では対応するボタンなし
    }

    // 初期状態のマッピングを設定
    UpdateKeyboardMapping(false, false);

    keyboardInitialized = true;
}

// ピッチに応じて鍵盤のマッピングを更新
void UpdateKeyboardMapping(bool semitoneUp, bool octaveUp) {
    // すべての鍵盤のボタンマッピングをリセット
    for (int i = 0; i < NUM_KEYS; i++) {
        keyboard[i].buttonId = BTN_NONE;
    }

    // ピッチに応じたオフセットを計算
    int offset = 0;
    if (semitoneUp) offset += 1;
    if (octaveUp) offset += 12;

    // 基本のマッピング（C4から始まる）
    int baseC4 = 0; // C4のインデックス

    // オフセットを適用したマッピング
    int c4Index = (baseC4 + offset) % NUM_KEYS;
    int d4Index = (baseC4 + 2 + offset) % NUM_KEYS;
    int e4Index = (baseC4 + 4 + offset) % NUM_KEYS;
    int f4Index = (baseC4 + 5 + offset) % NUM_KEYS;
    int g4Index = (baseC4 + 7 + offset) % NUM_KEYS;
    int a4Index = (baseC4 + 9 + offset) % NUM_KEYS;
    int b4Index = (baseC4 + 11 + offset) % NUM_KEYS;
    int c5Index = (baseC4 + 12 + offset) % NUM_KEYS;

    // ボタンと鍵盤のマッピングを更新
    keyboard[c4Index].buttonId = BTN_DPAD_DOWN;  // C4 - DPAD_DOWN
    keyboard[d4Index].buttonId = BTN_DPAD_LEFT;  // D4 - DPAD_LEFT
    keyboard[e4Index].buttonId = BTN_DPAD_UP;    // E4 - DPAD_UP
    keyboard[f4Index].buttonId = BTN_DPAD_RIGHT; // F4 - DPAD_RIGHT
    keyboard[g4Index].buttonId = BTN_X;          // G4 - X
    keyboard[a4Index].buttonId = BTN_A;          // A4 - A
    keyboard[b4Index].buttonId = BTN_B;          // B4 - B
    keyboard[c5Index].buttonId = BTN_Y;          // C5 - Y
}

// 鍵盤の描画
void DrawPianoKeyboard(void) {
    // 白鍵を先に描画
    for (int i = 0; i < NUM_KEYS; i++) {
        if (keyboard[i].type == KEY_WHITE) {
            Color keyColor = keyboard[i].isPressed ? LIGHTGRAY : WHITE;
            DrawRectangleRec(keyboard[i].rect, keyColor);
            DrawRectangleLinesEx(keyboard[i].rect, 1.0f, BLACK);
        }
    }

    // 黒鍵を後に描画（重ねて表示するため）
    for (int i = 0; i < NUM_KEYS; i++) {
        if (keyboard[i].type == KEY_BLACK) {
            Color keyColor = keyboard[i].isPressed ? DARKGRAY : BLACK;
            DrawRectangleRec(keyboard[i].rect, keyColor);
        }
    }
}

// 鍵盤の状態を更新
void UpdatePianoKeyboard(void) {
    // すべての鍵盤の状態をリセット
    for (int i = 0; i < NUM_KEYS; i++) {
        keyboard[i].isPressed = false;
    }

    // ゲームパッドが接続されている場合（実際のコントローラーのみ）
    int gamepadId = FindFirstRealGamepad();
    if (gamepadId != -1) {
        // 各鍵盤について、対応するボタンが押されているかチェック
        for (int i = 0; i < NUM_KEYS; i++) {
            ButtonId buttonId = keyboard[i].buttonId;
            if (buttonId != BTN_NONE) {
                int gamepadButton = GetGamepadButton(buttonId);
                if (gamepadButton != -1 && IsGamepadButtonDown(gamepadId, gamepadButton)) {
                    keyboard[i].isPressed = true;
                }
            }
        }
    }
}

// --- CPS Graph Drawing Function ---
void DrawCPSGraph(void) {
    // --- Smoothly Interpolate Graph Scale ---
    float dt = GetFrameTime(); // デルタタイムを取得
    graph_min_cps += (target_graph_min_cps - graph_min_cps) * GRAPH_SCALE_SMOOTH_FACTOR * dt; // デルタタイムを使用して補間
    graph_max_cps += (target_graph_max_cps - graph_max_cps) * GRAPH_SCALE_SMOOTH_FACTOR * dt; // デルタタイムを使用して補間
    // --- End Interpolation ---


    // Draw graph background and border
    DrawRectangle(GRAPH_X, GRAPH_Y, GRAPH_WIDTH, GRAPH_HEIGHT, Fade(LIGHTGRAY, 0.5f));
    DrawRectangleLines(GRAPH_X, GRAPH_Y, GRAPH_WIDTH, GRAPH_HEIGHT, GRAY);

    // Draw CPS label and dynamic range (using interpolated values)
    DrawText(TextFormat("CPS: %.1f", current_cps), GRAPH_X + 5, GRAPH_Y + 5, 10, BLACK);
    DrawText(TextFormat("Range: %.1f-%.1f", graph_min_cps, graph_max_cps), GRAPH_X + GRAPH_WIDTH - 80, GRAPH_Y + 5, 10, DARKGRAY);


    // Draw the graph line (using interpolated min/max)
    Vector2 prev_point = {0};
    float range = graph_max_cps - graph_min_cps;
    if (range <= 0) range = 1.0f; // Avoid division by zero if min == max

    for (int i = 0; i < GRAPH_HISTORY_LENGTH; i++) {
        int history_idx = (cps_history_index + i) % GRAPH_HISTORY_LENGTH; // Start from the oldest point
        float cps_val = cps_history[history_idx];

        // Calculate point coordinates based on dynamic min/max
        float point_x = (float)GRAPH_X + ((float)i / (GRAPH_HISTORY_LENGTH - 1)) * GRAPH_WIDTH;
        // Scale Y based on graph_min_cps and graph_max_cps
        float normalized_y = (cps_val - graph_min_cps) / range;
        // Clamp normalized_y between 0 and 1 in case cps_val is outside the calculated range slightly
        normalized_y = fmaxf(0.0f, fminf(1.0f, normalized_y));
        float point_y = (float)GRAPH_Y + GRAPH_HEIGHT - (normalized_y * GRAPH_HEIGHT);

        Vector2 current_point = { point_x, point_y };

        if (i > 0) {
            // Use DrawLineEx for thickness
            DrawLineEx(prev_point, current_point, GRAPH_LINE_THICKNESS, MAROON);
        }
        prev_point = current_point;
    }
}

int main(void)
{
    InitWindow(SCREEN_WIDTH, SCREEN_HEIGHT, "raylib Controller Synth");
    InitAudioDevice();

    // Load wave from memory
    Wave wavA4 = LoadWaveFromMemory(".mp3", a4_data, a4_data_size);
    Wave wavB4 = LoadWaveFromMemory(".mp3", b4_data, b4_data_size);
    Wave wavC4 = LoadWaveFromMemory(".mp3", c4_data, c4_data_size);
    Wave wavC5 = LoadWaveFromMemory(".mp3", c5_data, c5_data_size);
    Wave wavD4 = LoadWaveFromMemory(".mp3", d4_data, d4_data_size);
    Wave wavE4 = LoadWaveFromMemory(".mp3", e4_data, e4_data_size);
    Wave wavF4 = LoadWaveFromMemory(".mp3", f4_data, f4_data_size);
    Wave wavG4 = LoadWaveFromMemory(".mp3", g4_data, g4_data_size);

    // load sound
    Sound sndA4 = LoadSoundFromWave(wavA4);
    Sound sndB4 = LoadSoundFromWave(wavB4);
    Sound sndC4 = LoadSoundFromWave(wavC4);
    Sound sndC5 = LoadSoundFromWave(wavC5);
    Sound sndD4 = LoadSoundFromWave(wavD4);
    Sound sndE4 = LoadSoundFromWave(wavE4);
    Sound sndF4 = LoadSoundFromWave(wavF4);
    Sound sndG4 = LoadSoundFromWave(wavG4);

    // Unload wave data after loading sounds
    UnloadWave(wavA4);
    UnloadWave(wavB4);
    UnloadWave(wavC4);
    UnloadWave(wavC5);
    UnloadWave(wavD4);
    UnloadWave(wavE4);
    UnloadWave(wavF4);
    UnloadWave(wavG4);

    SetTargetFPS(0);

    // 鍵盤の初期化
    InitPianoKeyboard();

    // --- Array to hold state for all potentially active bars ---
    static BarInfo active_bars[MAX_ACTIVE_BARS] = {0}; // Initialize all to idle

    while (!WindowShouldClose())
    {
        float dt = GetFrameTime(); // Get delta time once per frame

        // --- Update CPS Calculation ---
        // Calculates current_cps and target scale factors every frame
        UpdateCPS();

        // --- Update Graph History at Fixed Interval ---
        graph_update_timer += dt;
        while (graph_update_timer >= GRAPH_UPDATE_INTERVAL)
        {
            // Store the latest calculated CPS into history
            cps_history[cps_history_index] = current_cps;
            cps_history_index = (cps_history_index + 1) % GRAPH_HISTORY_LENGTH;
            graph_update_timer -= GRAPH_UPDATE_INTERVAL; // Subtract interval, don't reset to 0
        }
        // --- End Graph History Update ---

        // 鍵盤の状態を更新
        UpdatePianoKeyboard();


        // --- Record Mouse Click Timestamps for CPS (Debug) ---
        if (IsMouseButtonPressed(MOUSE_LEFT_BUTTON)) {
            press_timestamps[timestamp_index] = GetTime();
            timestamp_index = (timestamp_index + 1) % MAX_TIMESTAMPS;
            if (timestamp_count < MAX_TIMESTAMPS) {
                timestamp_count++;
            }
        }
        // --- End Record Mouse Click ---


        BeginDrawing();
        ClearBackground(RAYWHITE);

        // --- Draw Controller Base Shapes (Simplified & Repositioned) ---

        // Shoulder Buttons (Static Background)
        DrawRectangleRec((Rectangle){POS_LB.x - SHOULDER_BTN_SIZE.x/2, POS_LB.y - SHOULDER_BTN_SIZE.y/2, SHOULDER_BTN_SIZE.x, SHOULDER_BTN_SIZE.y}, GRAY);
        DrawRectangleLinesEx((Rectangle){POS_LB.x - SHOULDER_BTN_SIZE.x/2, POS_LB.y - SHOULDER_BTN_SIZE.y/2, SHOULDER_BTN_SIZE.x, SHOULDER_BTN_SIZE.y}, 1.5f, BLACK);
        DrawText("LB", POS_LB.x - MeasureText("LB", LABEL_FONT_SIZE)/2, POS_LB.y - LABEL_FONT_SIZE/2, LABEL_FONT_SIZE, BLACK); // LB Label
        DrawRectangleRec((Rectangle){POS_RB.x - SHOULDER_BTN_SIZE.x/2, POS_RB.y - SHOULDER_BTN_SIZE.y/2, SHOULDER_BTN_SIZE.x, SHOULDER_BTN_SIZE.y}, GRAY);
        DrawRectangleLinesEx((Rectangle){POS_RB.x - SHOULDER_BTN_SIZE.x/2, POS_RB.y - SHOULDER_BTN_SIZE.y/2, SHOULDER_BTN_SIZE.x, SHOULDER_BTN_SIZE.y}, 1.5f, BLACK);
        DrawText("RB", POS_RB.x - MeasureText("RB", LABEL_FONT_SIZE)/2, POS_RB.y - LABEL_FONT_SIZE/2, LABEL_FONT_SIZE, BLACK); // RB Label

        // D-Pad Base (Cross shape)
        DrawRectangleRec((Rectangle){POS_DPAD_CENTER.x - DPAD_ARM_WIDTH/2, POS_DPAD_CENTER.y - DPAD_ARM_LENGTH, DPAD_ARM_WIDTH, DPAD_ARM_LENGTH*2}, GRAY); // Vertical arm
        DrawRectangleRec((Rectangle){POS_DPAD_CENTER.x - DPAD_ARM_LENGTH, POS_DPAD_CENTER.y - DPAD_ARM_WIDTH/2, DPAD_ARM_LENGTH*2, DPAD_ARM_WIDTH}, GRAY); // Horizontal arm
        DrawLineEx((Vector2){POS_DPAD_CENTER.x, POS_DPAD_CENTER.y - DPAD_ARM_LENGTH}, (Vector2){POS_DPAD_CENTER.x, POS_DPAD_CENTER.y + DPAD_ARM_LENGTH}, 2, BLACK); // Vertical line
        DrawLineEx((Vector2){POS_DPAD_CENTER.x - DPAD_ARM_LENGTH, POS_DPAD_CENTER.y}, (Vector2){POS_DPAD_CENTER.x + DPAD_ARM_LENGTH, POS_DPAD_CENTER.y}, 2, BLACK); // Horizontal line

        // Face Buttons (Static Background)
        DrawCircleV(POS_A, BUTTON_RADIUS, DARKGREEN);
        DrawCircleLines(POS_A.x, POS_A.y, BUTTON_RADIUS, BLACK);
        DrawText("A", POS_A.x - MeasureText("A", LABEL_FONT_SIZE)/2, POS_A.y - LABEL_FONT_SIZE/2, LABEL_FONT_SIZE, WHITE); // A Label
        DrawCircleV(POS_B, BUTTON_RADIUS, MAROON);
        DrawCircleLines(POS_B.x, POS_B.y, BUTTON_RADIUS, BLACK);
        DrawText("B", POS_B.x - MeasureText("B", LABEL_FONT_SIZE)/2, POS_B.y - LABEL_FONT_SIZE/2, LABEL_FONT_SIZE, WHITE); // B Label
        DrawCircleV(POS_X, BUTTON_RADIUS, DARKBLUE);
        DrawCircleLines(POS_X.x, POS_X.y, BUTTON_RADIUS, BLACK);
        DrawText("X", POS_X.x - MeasureText("X", LABEL_FONT_SIZE)/2, POS_X.y - LABEL_FONT_SIZE/2, LABEL_FONT_SIZE, WHITE); // X Label
        DrawCircleV(POS_Y, BUTTON_RADIUS, GOLD);
        DrawCircleLines(POS_Y.x, POS_Y.y, BUTTON_RADIUS, BLACK);
        DrawText("Y", POS_Y.x - MeasureText("Y", LABEL_FONT_SIZE)/2, POS_Y.y - LABEL_FONT_SIZE/2, LABEL_FONT_SIZE, BLACK); // Y Label (Black for contrast)
        // --- End Controller Base Shapes ---

        // --- Draw CPS Graph ---
        DrawCPSGraph(); // Draw the graph

        int gamepadId = FindFirstRealGamepad();
        if (gamepadId != -1)
        {
            // Debug: Show gamepad name (only once per second to avoid spam)
            static double lastDebugTime = 0;
            if (GetTime() - lastDebugTime > 1.0) {
                printf("Real gamepad detected: %s (ID: %d)\n", GetGamepadName(gamepadId), gamepadId);
                lastDebugTime = GetTime();
            }

            // --- Calculate Pitch Modifier ---
            float pitch = 1.0f;
            // Check LB/RB for pitch modifier *before* the main button loop
            bool lb_down_pitch = IsGamepadButtonDown(gamepadId, GAMEPAD_BUTTON_LEFT_TRIGGER_1);
            bool rb_down_pitch = IsGamepadButtonDown(gamepadId, GAMEPAD_BUTTON_RIGHT_TRIGGER_1);

            // ピッチ状態が変化した場合、鍵盤のマッピングを更新
            if (lb_down_pitch != isPitchSemitoneUp || rb_down_pitch != isPitchOctaveUp) {
                isPitchSemitoneUp = lb_down_pitch;
                isPitchOctaveUp = rb_down_pitch;
                UpdateKeyboardMapping(isPitchSemitoneUp, isPitchOctaveUp);
            }
            if (rb_down_pitch) pitch *= PITCH_OCTAVE_UP;
            if (lb_down_pitch) pitch *= PITCH_SEMITONE_UP;

            // --- Update Bar States based on Button Input ---
            for (ButtonId i = BTN_A; i < NUM_BUTTON_IDS; i++)
            {
                int gamepad_button = GetGamepadButton(i);
                if (gamepad_button == -1) continue;

                // --- Record Button Press Timestamps for CPS ---
                // Exclude modifier buttons (LB/RB) from CPS calculation
                if (i != BTN_LB && i != BTN_RB && IsGamepadButtonPressed(gamepadId, gamepad_button)) {
                    press_timestamps[timestamp_index] = GetTime();
                    timestamp_index = (timestamp_index + 1) % MAX_TIMESTAMPS;
                    if (timestamp_count < MAX_TIMESTAMPS) {
                        timestamp_count++;
                    }
                }
                // --- End Record Button Press ---

                bool is_down = IsGamepadButtonDown(gamepadId, gamepad_button);
                bool is_released = IsGamepadButtonReleased(gamepadId, gamepad_button);

                // Check if a flowing bar for this button already exists
                bool flowing_exists = false;
                int flowing_bar_index = -1;
                for (int j = 0; j < MAX_ACTIVE_BARS; j++) {
                    if (active_bars[j].state == BAR_FLOWING && active_bars[j].button_id == i) {
                        flowing_exists = true;
                        flowing_bar_index = j;
                        break;
                    }
                }

                if (is_down)
                {
                    if (!flowing_exists) // Only start a new flow if one isn't already active for this button
                    {
                        // Find an idle slot
                        int idle_slot = -1;
                        for (int j = 0; j < MAX_ACTIVE_BARS; j++) {
                            if (active_bars[j].state == BAR_IDLE) {
                                idle_slot = j;
                                break;
                            }
                        }

                        if (idle_slot != -1) {
                            // Start new flow in the idle slot
                            active_bars[idle_slot].state = BAR_FLOWING;
                            active_bars[idle_slot].button_id = i;
                            active_bars[idle_slot].progress = 0.0f;
                            active_bars[idle_slot].visual_start_x = 0.0f; // Initialize just in case
                            active_bars[idle_slot].visual_end_x = 0.0f;
                        }
                        // If no idle slot, the new bar cannot start (pool full)
                    }
                }
                else if (is_released) // Button is released
                {
                    if (flowing_bar_index != -1) // If a flowing bar exists for this button
                    {
                        // Start fading out the flowing bar
                        BarInfo *bar_to_fade = &active_bars[flowing_bar_index];
                        bar_to_fade->state = BAR_FADING_OUT;
                        float current_width = fminf(bar_to_fade->progress * SCREEN_WIDTH, SCREEN_WIDTH);
                        bar_to_fade->visual_start_x = 0.0f;
                        bar_to_fade->visual_end_x = current_width;
                        bar_to_fade->progress = 0.0f; // Reset progress
                    }
                }
            } // End button input loop

            // --- Animate and Draw All Active Bars ---
            float delta_x = BAR_SPEED * SCREEN_WIDTH * dt; // Use dt

            for (int j = 0; j < MAX_ACTIVE_BARS; j++)
            {
                BarInfo *bar = &active_bars[j];
                if (bar->state == BAR_IDLE) continue; // Skip idle slots

                Color color = BUTTON_BAR_COLORS[bar->button_id];
                int y = BUTTON_BAR_Y[bar->button_id];

                if (bar->state == BAR_FLOWING)
                {
                    bar->progress += BAR_SPEED * dt; // Use dt
                    float draw_width = fminf(bar->progress * SCREEN_WIDTH, SCREEN_WIDTH);
                    float start_x = 0.0f;
                    if (draw_width > 0) {
                        DrawRectangle(start_x, y, draw_width, BAR_HEIGHT, color);
                    }
                }
                else // BAR_FADING_OUT
                {
                    bar->visual_start_x += delta_x; // delta_x is already based on dt
                    bar->visual_end_x += delta_x;   // delta_x is already based on dt

                    if (bar->visual_start_x >= SCREEN_WIDTH)
                    {
                        bar->state = BAR_IDLE; // Mark slot as free
                        bar->button_id = BTN_NONE; // Clear button association
                        continue;
                    }

                    float draw_start = fmaxf(0.0f, bar->visual_start_x);
                    float draw_end = fminf(SCREEN_WIDTH, bar->visual_end_x);
                    float draw_width = draw_end - draw_start;

                    if (draw_width > 0)
                    {
                        DrawRectangle(draw_start, y, draw_width, BAR_HEIGHT, color);
                    }
                }
            } // End active_bars animation loop

            // 鍵盤を描画
            DrawPianoKeyboard();

            // --- Draw Indicators and Handle Sound (Simplified) ---
            // This part remains mostly the same, just remove the bar state update logic from here
            for (ButtonId i = BTN_A; i < NUM_BUTTON_IDS; i++)
            {
                int gamepad_button = GetGamepadButton(i);
                if (gamepad_button == -1) continue;
                bool is_down = IsGamepadButtonDown(gamepadId, gamepad_button);

                if (is_down) {
                    switch (i) {
                        case BTN_A: DrawCircleV(POS_A, INDICATOR_RADIUS, LIME); if (!IsSoundPlaying(sndA4)) { SetSoundPitch(sndA4, pitch); PlaySound(sndA4); } break;
                        case BTN_B: DrawCircleV(POS_B, INDICATOR_RADIUS, PINK); if (!IsSoundPlaying(sndB4)) { SetSoundPitch(sndB4, pitch); PlaySound(sndB4); } break;
                        case BTN_X: DrawCircleV(POS_X, INDICATOR_RADIUS, SKYBLUE); if (!IsSoundPlaying(sndG4)) { SetSoundPitch(sndG4, pitch); PlaySound(sndG4); } break; // Note: X is sndG4
                        case BTN_Y: DrawCircleV(POS_Y, INDICATOR_RADIUS, ORANGE); if (!IsSoundPlaying(sndC5)) { SetSoundPitch(sndC5, pitch); PlaySound(sndC5); } break;
                        case BTN_DPAD_UP: DrawRectangle(POS_DPAD_CENTER.x - DPAD_ARM_WIDTH/2, POS_DPAD_CENTER.y - DPAD_ARM_LENGTH, DPAD_ARM_WIDTH, DPAD_ARM_LENGTH, SKYBLUE); if (!IsSoundPlaying(sndE4)) { SetSoundPitch(sndE4, pitch); PlaySound(sndE4); } break;
                        case BTN_DPAD_DOWN: DrawRectangle(POS_DPAD_CENTER.x - DPAD_ARM_WIDTH/2, POS_DPAD_CENTER.y, DPAD_ARM_WIDTH, DPAD_ARM_LENGTH, SKYBLUE); if (!IsSoundPlaying(sndC4)) { SetSoundPitch(sndC4, pitch); PlaySound(sndC4); } break;
                        case BTN_DPAD_LEFT: DrawRectangle(POS_DPAD_CENTER.x - DPAD_ARM_LENGTH, POS_DPAD_CENTER.y - DPAD_ARM_WIDTH/2, DPAD_ARM_LENGTH, DPAD_ARM_WIDTH, SKYBLUE); if (!IsSoundPlaying(sndD4)) { SetSoundPitch(sndD4, pitch); PlaySound(sndD4); } break;
                        case BTN_DPAD_RIGHT: DrawRectangle(POS_DPAD_CENTER.x, POS_DPAD_CENTER.y - DPAD_ARM_WIDTH/2, DPAD_ARM_LENGTH, DPAD_ARM_WIDTH, SKYBLUE); if (!IsSoundPlaying(sndF4)) { SetSoundPitch(sndF4, pitch); PlaySound(sndF4); } break;
                        case BTN_LB: DrawRectangleRec((Rectangle){POS_LB.x - SHOULDER_BTN_SIZE.x/2, POS_LB.y - SHOULDER_BTN_SIZE.y/2, SHOULDER_BTN_SIZE.x, SHOULDER_BTN_SIZE.y}, RED); /* No sound for LB */ break;
                        case BTN_RB: DrawRectangleRec((Rectangle){POS_RB.x - SHOULDER_BTN_SIZE.x/2, POS_RB.y - SHOULDER_BTN_SIZE.y/2, SHOULDER_BTN_SIZE.x, SHOULDER_BTN_SIZE.y}, RED); /* No sound for RB */ break;
                        default: break;
                    }
                } else { // Button is released
                     switch (i) {
                        case BTN_A: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndA4); break;
                        case BTN_B: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndB4); break;
                        case BTN_X: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndG4); break; // Note: X is sndG4
                        case BTN_Y: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndC5); break;
                        case BTN_DPAD_UP: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndE4); break;
                        case BTN_DPAD_DOWN: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndC4); break;
                        case BTN_DPAD_LEFT: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndD4); break;
                        case BTN_DPAD_RIGHT: if (IsGamepadButtonReleased(gamepadId, gamepad_button)) StopSound(sndF4); break;
                        // LB/RB don't have sounds to stop
                        default: break;
                     }
                }
            } // End indicator/sound loop

        } // End if RealGamepad
        else
        {
            DrawText("Real gamepad not detected", 10, 10, 20, GRAY); // Keep this message

            // Debug: Show additional gamepad detection info
            static double lastDebugTime = 0;
            if (GetTime() - lastDebugTime > 2.0) {
                printf("Debug: Checking for real gamepads...\n");
                for (int i = 0; i < 4; i++) {
                    if (IsGamepadAvailable(i)) {
                        const char* name = GetGamepadName(i);
                        if (IsRealGamepad(i)) {
                            printf("  Real gamepad %d found: %s\n", i, name);
                        } else {
                            printf("  Filtered device %d: %s (not a real gamepad)\n", i, name);
                        }
                    }
                }
                printf("  No real gamepads detected. Make sure:\n");
                printf("  1. Controller is connected and powered on\n");
                printf("  2. User is in 'input' group: groups | grep input\n");
                printf("  3. /dev/input/ devices are accessible\n");
                lastDebugTime = GetTime();
            }

            // Reset all bars if gamepad disconnects
            for (int j = 0; j < MAX_ACTIVE_BARS; j++) {
                active_bars[j].state = BAR_IDLE;
                active_bars[j].button_id = BTN_NONE;
            }

            // 鍵盤を描画（ゲームパッドが接続されていなくても表示）
            DrawPianoKeyboard();
        }

        EndDrawing();
    } // End while loop

    // Unload sounds
    UnloadSound(sndA4);
    UnloadSound(sndB4);
    UnloadSound(sndC4);
    UnloadSound(sndC5);
    UnloadSound(sndD4);
    UnloadSound(sndE4);
    UnloadSound(sndF4);
    UnloadSound(sndG4);

    CloseAudioDevice(); // Close audio device
    CloseWindow();

    return 0;
}
