{
    files = {
        "src/main.c"
    },
    depfiles = "main.o: src/main.c  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/a4.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/b4.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/c4.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/c5.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/d4.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/e4.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/f4.mp3.h  build/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c/g4.mp3.h\
",
    values = {
        "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        {
            "-m64",
            "-std=c99",
            "-Isrc",
            "-Ibuild/.gens/controller-synth/linux/x86_64/release/rules/utils/bin2c",
            "-DPLATFORM_DESKTOP",
            "-DGRAPHICS_API_OPENGL_33",
            "-D_GNU_SOURCE",
            "-D_DEFAULT_SOURCE",
            "-DRAYGUI_IMPLEMENTATION",
            "-isystem",
            "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/include",
            "-isystem",
            "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
            "-isystem",
            "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include",
            "-isystem",
            "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include",
            "-isystem",
            "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include",
            "-isystem",
            "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include",
            "-isystem",
            "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include",
            "-isystem",
            "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include",
            "-Wall",
            "-Wextra",
            "-O2"
        }
    },
    depfiles_format = "gcc"
}