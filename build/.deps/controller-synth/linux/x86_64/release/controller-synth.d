{
    files = {
        "build/.objs/controller-synth/linux/x86_64/release/src/main.c.o"
    },
    values = {
        "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        {
            "-m64",
            "-L/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib",
            "-L/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib",
            "-L/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib",
            "-L/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib",
            "-L/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib",
            "-L/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib",
            "-L/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib",
            "-L/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib",
            "-L/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib",
            "-L/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib",
            "-L/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib",
            "-L/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib",
            "-L/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib",
            "-lraylib",
            "-lXrender",
            "-lXfixes",
            "-lXext",
            "-lX11-xcb",
            "-lxcb",
            "-lxcb-composite",
            "-lxcb-damage",
            "-lxcb-dbe",
            "-lxcb-dpms",
            "-lxcb-dri2",
            "-lxcb-dri3",
            "-lxcb-present",
            "-lxcb-glx",
            "-lxcb-randr",
            "-lxcb-record",
            "-lxcb-render",
            "-lxcb-res",
            "-lxcb-screensaver",
            "-lxcb-shape",
            "-lxcb-shm",
            "-lxcb-sync",
            "-lxcb-xevie",
            "-lxcb-xf86dri",
            "-lxcb-xfixes",
            "-lxcb-xinerama",
            "-lxcb-xinput",
            "-lxcb-xkb",
            "-lxcb-xtest",
            "-lxcb-xv",
            "-lxcb-xvmc",
            "-lxcb-ge",
            "-lXau",
            "-lXdmcp",
            "-lOpenGL",
            "-lm",
            "-lpthread",
            "-lrt",
            "-lX11",
            "-lXrandr",
            "-lXinerama",
            "-lXi",
            "-lXxf86vm",
            "-lXcursor",
            "-lwayland-client",
            "-lwayland-cursor",
            "-lwayland-egl",
            "-lxkbcommon",
            "-lGL",
            "-lasound",
            "-ldl"
        }
    }
}