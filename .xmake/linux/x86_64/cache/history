{
    cmdlines = {
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake check",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake check",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake check",
        "xmake clean",
        "xmake build",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake check",
        "xmake clean",
        "xmake build",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.1/assets/config.lua",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake clean",
        "xmake build",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake build",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua",
        "xmake run",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/n5z5gcglwfjlhpkzmd9jpbwwihkahrsa-xmake-3.0.1/share/xmake/actions/build/cleaner.lua"
    }
}