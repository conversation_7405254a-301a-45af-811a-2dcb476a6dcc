{
    raylib = {
        __requirestr = "raylib",
        syslinks = "dl",
        links = {
            "raylib",
            "Xrandr",
            "Xinerama",
            "<PERSON>cursor",
            "Xrender",
            "Xi",
            "Xfixes",
            "Xext",
            "X11",
            "X11-xcb",
            "xcb",
            "xcb-composite",
            "xcb-damage",
            "xcb-dbe",
            "xcb-dpms",
            "xcb-dri2",
            "xcb-dri3",
            "xcb-present",
            "xcb-glx",
            "xcb-randr",
            "xcb-record",
            "xcb-render",
            "xcb-res",
            "xcb-screensaver",
            "xcb-shape",
            "xcb-shm",
            "xcb-sync",
            "xcb-xevie",
            "xcb-xf86dri",
            "xcb-xfixes",
            "xcb-xinerama",
            "xcb-xinput",
            "xcb-xkb",
            "xcb-xtest",
            "xcb-xv",
            "xcb-xvmc",
            "xcb-ge",
            "Xau",
            "Xdmcp",
            "OpenGL"
        },
        libfiles = {
            "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib/libraylib.so",
            "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so",
            "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so",
            "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so",
            "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib/libXrender.a",
            "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so",
            "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib/libXfixes.a",
            "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6.4.0",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1.0.0",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-composite.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-damage.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dbe.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dpms.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri2.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri3.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-present.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-glx.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-randr.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-record.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-render.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-res.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-screensaver.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shape.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shm.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-sync.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xevie.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xf86dri.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xfixes.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinerama.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinput.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xkb.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xtest.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xv.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xvmc.a",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-ge.a",
            "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib/libXau.a",
            "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib/libXdmcp.a",
            "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
        },
        version = "5.5.0",
        __requireconfs = {
            configs = {
                shared = false,
                opengl_version = "33"
            }
        },
        linkdirs = {
            "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib",
            "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib",
            "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib",
            "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib",
            "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib",
            "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib",
            "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib",
            "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib",
            "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib",
            "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib",
            "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
        },
        envs = {
            PYTHONPATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/lib/python3.13/site-packages"
            },
            PATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/Scripts",
                "/home/<USER>/.xmake/packages/n/ninja/v1.12.1/12c84655558847c09203ef9d0105b20d/bin",
                "/home/<USER>/.xmake/packages/c/cmake/4.0.3/b686e3b7911246e2bfa1b52ae27762fe/bin",
                "/home/<USER>/.xmake/packages/p/pkg-config/0.29.2/a0aba256d7674c1bbee15f0ee1534e92/bin",
                "/home/<USER>/.xmake/packages/p/pkg-config/0.29.2/a0aba256d7674c1bbee15f0ee1534e92/bin"
            },
            SSL_CERT_FILE = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20/cacert.pem"
            },
            SSL_CERT_DIR = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20"
            },
            LD_LIBRARY_PATH = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            }
        },
        shared = true,
        sysincludedirs = {
            "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/include",
            "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
            "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include",
            "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include",
            "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include",
            "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include",
            "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include",
            "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include",
            "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include",
            "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include",
            "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include",
            "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include",
            "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include",
            "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include",
            "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include",
            "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
        },
        __enabled = true
    }
}