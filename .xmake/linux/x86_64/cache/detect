{
    find_programver = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "14.3.0",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "14.3.0"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0"] = {
            ["-B"] = true,
            ["-print-libgcc-file-name"] = true,
            ["--version"] = true,
            ["-dumpversion"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-print-multi-directory"] = true,
            ["-E"] = true,
            ["-o"] = true,
            ["--target-help"] = true,
            ["-print-multi-lib"] = true,
            ["-print-multiarch"] = true,
            ["-Xassembler"] = true,
            ["-c"] = true,
            ["-dumpmachine"] = true,
            ["-pipe"] = true,
            ["-shared"] = true,
            ["-v"] = true,
            ["-print-sysroot"] = true,
            ["-Xpreprocessor"] = true,
            ["-print-multi-os-directory"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-dumpspecs"] = true,
            ["--help"] = true,
            ["-pass-exit-codes"] = true,
            ["-S"] = true,
            ["-pie"] = true,
            ["-Xlinker"] = true,
            ["--param"] = true,
            ["-time"] = true,
            ["-print-search-dirs"] = true,
            ["-x"] = true,
            ["-save-temps"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_ld__-m64 -m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-O2"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wall"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wextra"] = true
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    find_program_fetch_package_system = {
        cmake = "/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"
    },
    find_programver_fetch_package_system = {
        ["/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"] = "0.29.2",
        ["/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"] = "3.31.7"
    },
    find_program_fetch_package_xmake = {
        python3 = false,
        ninja = false,
        python = false,
        cmake = false,
        python2 = false,
        ["pkg-config"] = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0"] = {
            ["-flto"] = true,
            ["--orphan-handling"] = true,
            ["--error-handling-script"] = true,
            ["--dynamic-linker"] = true,
            ["--stats"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--default-script"] = true,
            ["--remap-inputs-file"] = true,
            ["-c"] = true,
            ["-dp"] = true,
            ["-L"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--out-implib"] = true,
            ["--just-symbols"] = true,
            ["--no-warn-mismatch"] = true,
            ["--require-defined"] = true,
            ["--demangle"] = true,
            ["-Ttext"] = true,
            ["-T"] = true,
            ["--undefined-version"] = true,
            ["--error-rwx-segments"] = true,
            ["--cref"] = true,
            ["-A"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-gc-sections"] = true,
            ["--warn-common"] = true,
            ["-Bshareable"] = true,
            ["-I"] = true,
            ["--print-map-discarded"] = true,
            ["--no-warnings"] = true,
            ["--trace-symbol"] = true,
            ["--print-sysroot"] = true,
            ["--no-error-execstack"] = true,
            ["-f"] = true,
            ["-Bsymbolic"] = true,
            ["--default-imported-symver"] = true,
            ["--gc-keep-exported"] = true,
            ["--no-rosegment"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["--default-symver"] = true,
            ["--pop-state"] = true,
            ["-init"] = true,
            ["--version-script"] = true,
            ["-m"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["-Ur"] = true,
            ["--sort-section"] = true,
            ["-e"] = true,
            ["-qmagic"] = true,
            ["--check-sections"] = true,
            ["-nostdlib"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--push-state"] = true,
            ["--warn-alternate-em"] = true,
            ["--end-group"] = true,
            ["--no-dynamic-linker"] = true,
            ["--print-memory-usage"] = true,
            ["-R"] = true,
            ["--force-exe-suffix"] = true,
            ["--no-undefined"] = true,
            ["-G"] = true,
            ["-plugin-save-temps"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--disable-linker-version"] = true,
            ["--no-error-rwx-segments"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--disable-new-dtags"] = true,
            ["--fatal-warnings"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--no-print-map-locals"] = true,
            ["-z"] = true,
            ["--split-by-file"] = true,
            ["--strip-all"] = true,
            ["--defsym"] = true,
            ["--no-as-needed"] = true,
            ["--gpsize"] = true,
            ["--gc-sections"] = true,
            ["--no-whole-archive"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--no-demangle"] = true,
            ["-O"] = true,
            ["--print-output-format"] = true,
            ["-u"] = true,
            ["-Tldata-segment"] = true,
            ["--relax"] = true,
            ["--library"] = true,
            ["--no-omagic"] = true,
            ["--as-needed"] = true,
            ["-o"] = true,
            ["--discard-locals"] = true,
            ["--version-exports-section"] = true,
            ["-Bgroup"] = true,
            ["--no-fatal-warnings"] = true,
            ["--section-ordering-file"] = true,
            ["--no-export-dynamic"] = true,
            ["-debug"] = true,
            ["-fini"] = true,
            ["-Y"] = true,
            ["--unique"] = true,
            ["--nmagic"] = true,
            ["--architecture"] = true,
            ["--no-strip-discarded"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["-Qy"] = true,
            ["--wrap"] = true,
            ["--enable-new-dtags"] = true,
            ["--script"] = true,
            ["--auxiliary"] = true,
            ["--rosegment"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["-g"] = true,
            ["-P"] = true,
            ["--no-map-whole-files"] = true,
            ["--warn-execstack-objects"] = true,
            ["--emit-relocs"] = true,
            ["--version"] = true,
            ["--eh-frame-hdr"] = true,
            ["--remap-inputs"] = true,
            ["--output"] = true,
            ["--mri-script"] = true,
            ["--discard-all"] = true,
            ["-b"] = true,
            ["--dependency-file"] = true,
            ["--whole-archive"] = true,
            ["--no-define-common"] = true,
            ["-assert"] = true,
            ["--verbose"] = true,
            ["-Ttext-segment"] = true,
            ["--retain-symbols-file"] = true,
            ["-rpath-link"] = true,
            ["-Tbss"] = true,
            ["--no-keep-memory"] = true,
            ["-plugin-opt"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--warn-execstack"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--no-relax"] = true,
            ["--omagic"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["-EL"] = true,
            ["--error-execstack"] = true,
            ["-h"] = true,
            ["--target-help"] = true,
            ["--warn-textrel"] = true,
            ["--split-by-reloc"] = true,
            ["--help"] = true,
            ["--task-link"] = true,
            ["-Map"] = true,
            ["--no-check-sections"] = true,
            ["-plugin"] = true,
            ["--print-map-locals"] = true,
            ["--pic-executable"] = true,
            ["-F"] = true,
            ["--force-group-allocation"] = true,
            ["-a"] = true,
            ["--strip-debug"] = true,
            ["-y"] = true,
            ["--warn-section-align"] = true,
            ["--oformat"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["-Bno-symbolic"] = true,
            ["--no-undefined-version"] = true,
            ["--ctf-variables"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--no-print-map-discarded"] = true,
            ["--export-dynamic-symbol"] = true,
            ["-EB"] = true,
            ["--format"] = true,
            ["-Trodata-segment"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--strip-discarded"] = true,
            ["--start-group"] = true,
            ["-V"] = true,
            ["--trace"] = true,
            ["--traditional-format"] = true,
            ["--undefined"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--relocatable"] = true,
            ["--export-dynamic"] = true,
            ["--no-warn-execstack"] = true,
            ["--entry"] = true,
            ["--map-whole-files"] = true,
            ["--no-ctf-variables"] = true,
            ["--sort-common"] = true,
            ["-l"] = true,
            ["--dynamic-list"] = true,
            ["--print-map"] = true,
            ["-soname"] = true,
            ["--warn-multiple-gp"] = true,
            ["--no-print-gc-sections"] = true,
            ["-static"] = true,
            ["-dT"] = true,
            ["--warn-rwx-segments"] = true,
            ["-Tdata"] = true,
            ["-no-pie"] = true,
            ["--dynamic-list-data"] = true,
            ["--library-path"] = true,
            ["--image-base"] = true,
            ["--section-start"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--print-gc-sections"] = true,
            ["--warn-once"] = true,
            ["--error-unresolved-symbols"] = true,
            ["-rpath"] = true,
            ["--enable-linker-version"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--filter"] = true,
            ["--discard-none"] = true
        }
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    find_program = {
        git = "/nix/store/khdq35xh67vw7j7k9y4q9l0svxznhx1b-git-2.50.0/bin/git",
        gzip = "/nix/store/31h4wbbp0yvsrciyw46iij6r4c0wc8fp-gzip-1.14/bin/gzip",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        dpkg = false,
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config",
        emerge = false,
        nim = false,
        brew = false,
        pacman = false,
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        tar = "/nix/store/r8d70l24x06f4fgq246wz2s8zvmj6vxb-gnutar-1.35/bin/tar"
    },
    find_package_linux_x86_64_fetch_package_xmake = {
        ["xmake::libxau_f406943012fc443596a2e2e23215839d_release_1.0.12_external"] = {
            links = {
                "Xau"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib"
            },
            version = "1.0.12",
            static = true,
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib/libXau.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include"
            }
        },
        ["xmake::libxdmcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_1.1.5_external"] = {
            links = {
                "Xdmcp"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib"
            },
            version = "1.1.5",
            static = true,
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib/libXdmcp.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include"
            }
        },
        ["xmake::libxrandr_a36fcab100ed449e86944351e00c0384_release_1.5.4_external"] = false,
        ["xmake::ca-certificates_e9ba281ae183402a80dc072d4d571c20_release_20250131_external"] = {
            version = "20250131"
        },
        ["xmake::opengl_4e0143c97b65425b855ad5fd03038b6a_release_latest_external"] = false,
        ["xmake::libxi_2f225dfd5e274482ae09ba4da0c185c5_release_1.8.2_external"] = false,
        ["xmake::libffi_46686cf534074f4ebf90e4ab1f9662dd_release_3.4.8_external"] = {
            version = "3.4.8",
            links = {
                "ffi"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib"
            },
            static = true,
            license = "MIT",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib/libffi.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/include"
            }
        },
        ["xmake::xcb-proto_eafb62cb3f37412ebe648eb0ab4b4b35_release_1.17.0_external"] = {
            version = "1.17.0"
        },
        ["xmake::xorgproto_3a1682704c034fbf87583b32455dde94_release_2023.2_external"] = {
            version = "2023.2",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include"
            }
        },
        ["xmake::libx11_5b3492e889e848b2b511e3f3031c5538_release_1.8.12_external"] = {
            syslinks = "dl",
            links = {
                "X11",
                "X11-xcb"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            },
            version = "1.8.12",
            shared = true,
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6.4.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1.0.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include"
            }
        },
        ["xmake::xtrans_588d85a9c99148b8ae87dee11b39ab40_release_1.6.0_external"] = {
            version = "1.6.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include"
            }
        },
        ["xmake::libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_6.0.1_external"] = {
            links = {
                "Xfixes"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib"
            },
            version = "6.0.1",
            static = true,
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib/libXfixes.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include"
            }
        },
        ["xmake::libxcb_27d2233a10b34ff5971001208db29e2c_release_1.17.0_external"] = {
            links = {
                "xcb",
                "xcb-composite",
                "xcb-damage",
                "xcb-dbe",
                "xcb-dpms",
                "xcb-dri2",
                "xcb-dri3",
                "xcb-present",
                "xcb-glx",
                "xcb-randr",
                "xcb-record",
                "xcb-render",
                "xcb-res",
                "xcb-screensaver",
                "xcb-shape",
                "xcb-shm",
                "xcb-sync",
                "xcb-xevie",
                "xcb-xf86dri",
                "xcb-xfixes",
                "xcb-xinerama",
                "xcb-xinput",
                "xcb-xkb",
                "xcb-xtest",
                "xcb-xv",
                "xcb-xvmc",
                "xcb-ge"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib"
            },
            version = "1.17.0",
            static = true,
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-composite.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-damage.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dbe.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dpms.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri2.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri3.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-present.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-glx.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-randr.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-record.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-render.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-res.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-screensaver.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shape.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shm.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-sync.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xevie.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xf86dri.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xfixes.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinerama.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinput.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xkb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xtest.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xv.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xvmc.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-ge.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include"
            }
        },
        ["xmake::libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_0.5_external"] = {
            version = "0.5"
        },
        ["xmake::util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_1.20.0_external"] = {
            version = "1.20.0"
        },
        ["xmake::raylib_efaf75c5cc7042868d9f9fc4196a080a_release_5.5_external"] = false,
        ["xmake::zlib_994fafa590ed48ac9f71516cc846d155_release_v1.3.1_external"] = {
            version = "v1.3.1",
            links = {
                "z"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib"
            },
            static = true,
            license = "zlib",
            libfiles = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib/libz.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include"
            }
        },
        ["xmake::openssl_6c51ab6278e2479b883dffafac69fdaf_release_1.1.1-w_external"] = {
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include"
            },
            syslinks = {
                "pthread",
                "dl"
            },
            links = {
                "ssl",
                "crypto"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib"
            },
            static = true,
            license = "Apache-2.0",
            libfiles = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libssl.a",
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libcrypto.a"
            },
            version = "1.1.1-w"
        },
        ["xmake::libxinerama_e539e5b3575342a89c7a931295fe40c5_release_1.1.5_external"] = false,
        ["xmake::libxrender_75df963310db406385383085b95b222e_release_0.9.12_external"] = {
            links = {
                "Xrender"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib"
            },
            version = "0.9.12",
            static = true,
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib/libXrender.a"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include"
            }
        },
        ["xmake::libxext_5a0d83a7724446099fee649c3d9afd32_release_1.3.6_external"] = false,
        ["xmake::libxcursor_665b359dc13241789f6ca7a586bc5127_release_1.2.3_external"] = false
    },
    find_package_linux_x86_64_fetch_package_system = {
        ["apt::libxinerama-dev_e539e5b3575342a89c7a931295fe40c5_release_external"] = false,
        ["apt::libxi-dev_2f225dfd5e274482ae09ba4da0c185c5_release_external"] = false,
        libxinerama_e539e5b3575342a89c7a931295fe40c5_release_external = {
            links = {
                "Xinerama"
            },
            linkdirs = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib"
            },
            version = "1.1.5",
            shared = true,
            libfiles = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include"
            }
        },
        ["apt::libxext-dev_5a0d83a7724446099fee649c3d9afd32_release_external"] = false,
        opengl_4e0143c97b65425b855ad5fd03038b6a_release_external = {
            links = {
                "OpenGL"
            },
            linkdirs = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
            },
            version = "4.5",
            shared = true,
            libfiles = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
            },
            sysincludedirs = {
                "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
            }
        },
        libxext_5a0d83a7724446099fee649c3d9afd32_release_external = {
            links = {
                "Xext"
            },
            linkdirs = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib"
            },
            version = "1.3.6",
            shared = true,
            libfiles = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include"
            }
        },
        libxrandr_a36fcab100ed449e86944351e00c0384_release_external = {
            links = {
                "Xrandr"
            },
            linkdirs = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib"
            },
            version = "1.5.4",
            shared = true,
            libfiles = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include"
            }
        },
        ["apt::libxrandr-dev_a36fcab100ed449e86944351e00c0384_release_external"] = false,
        libxcursor_665b359dc13241789f6ca7a586bc5127_release_external = {
            links = {
                "Xcursor"
            },
            linkdirs = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib"
            },
            version = "1.2.3",
            shared = true,
            libfiles = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include"
            }
        },
        libxi_2f225dfd5e274482ae09ba4da0c185c5_release_external = {
            links = {
                "Xi"
            },
            linkdirs = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib"
            },
            version = "1.8.2",
            shared = true,
            libfiles = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include"
            }
        },
        raylib_efaf75c5cc7042868d9f9fc4196a080a_release_external = {
            links = {
                "raylib"
            },
            linkdirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib"
            },
            version = "5.5.0",
            shared = true,
            libfiles = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib/libraylib.so"
            },
            sysincludedirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/include"
            }
        },
        ["apt::libxcursor-dev_665b359dc13241789f6ca7a586bc5127_release_external"] = false
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    }
}