{
    find_program_fetch_package_xmake = {
        ninja = false,
        python = false,
        cmake = false,
        python2 = false,
        ["pkg-config"] = false,
        python3 = false
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0"] = {
            ["-print-sysroot-headers-suffix"] = true,
            ["-E"] = true,
            ["-dumpspecs"] = true,
            ["-print-multi-lib"] = true,
            ["-print-multi-directory"] = true,
            ["-x"] = true,
            ["-Xassembler"] = true,
            ["--param"] = true,
            ["-save-temps"] = true,
            ["-dumpversion"] = true,
            ["-v"] = true,
            ["-print-search-dirs"] = true,
            ["-print-multi-os-directory"] = true,
            ["-pipe"] = true,
            ["-time"] = true,
            ["-no-canonical-prefixes"] = true,
            ["--target-help"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-print-multiarch"] = true,
            ["-Xlinker"] = true,
            ["-dumpmachine"] = true,
            ["--version"] = true,
            ["-o"] = true,
            ["-S"] = true,
            ["-pass-exit-codes"] = true,
            ["-B"] = true,
            ["-shared"] = true,
            ["-c"] = true,
            ["--help"] = true,
            ["-pie"] = true,
            ["-print-sysroot"] = true,
            ["-Xpreprocessor"] = true
        }
    },
    find_package_linux_x86_64_fetch_package_system = {
        opengl_4e0143c97b65425b855ad5fd03038b6a_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
            },
            links = {
                "OpenGL"
            },
            linkdirs = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
            },
            libfiles = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
            },
            version = "4.5"
        },
        ["apt::libxcursor-dev_665b359dc13241789f6ca7a586bc5127_release_external"] = false,
        libxext_5a0d83a7724446099fee649c3d9afd32_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include"
            },
            links = {
                "Xext"
            },
            linkdirs = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib"
            },
            libfiles = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so"
            },
            version = "1.3.6"
        },
        ["apt::libxrandr-dev_a36fcab100ed449e86944351e00c0384_release_external"] = false,
        libxi_2f225dfd5e274482ae09ba4da0c185c5_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include"
            },
            links = {
                "Xi"
            },
            linkdirs = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib"
            },
            libfiles = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so"
            },
            version = "1.8.2"
        },
        libxinerama_e539e5b3575342a89c7a931295fe40c5_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include"
            },
            links = {
                "Xinerama"
            },
            linkdirs = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib"
            },
            libfiles = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so"
            },
            version = "1.1.5"
        },
        ["apt::libxi-dev_2f225dfd5e274482ae09ba4da0c185c5_release_external"] = false,
        ["apt::libxext-dev_5a0d83a7724446099fee649c3d9afd32_release_external"] = false,
        raylib_efaf75c5cc7042868d9f9fc4196a080a_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/include"
            },
            links = {
                "raylib"
            },
            linkdirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib"
            },
            libfiles = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib/libraylib.so"
            },
            version = "5.5.0"
        },
        libxrandr_a36fcab100ed449e86944351e00c0384_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include"
            },
            links = {
                "Xrandr"
            },
            linkdirs = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib"
            },
            libfiles = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so"
            },
            version = "1.5.4"
        },
        ["apt::libxinerama-dev_e539e5b3575342a89c7a931295fe40c5_release_external"] = false,
        libxcursor_665b359dc13241789f6ca7a586bc5127_release_external = {
            shared = true,
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include"
            },
            links = {
                "Xcursor"
            },
            linkdirs = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib"
            },
            libfiles = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so"
            },
            version = "1.2.3"
        }
    },
    find_program_fetch_package_system = {
        cmake = "/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    find_program = {
        tar = "/nix/store/r8d70l24x06f4fgq246wz2s8zvmj6vxb-gnutar-1.35/bin/tar",
        nim = false,
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config",
        dpkg = false,
        brew = false,
        emerge = false,
        gzip = "/nix/store/31h4wbbp0yvsrciyw46iij6r4c0wc8fp-gzip-1.14/bin/gzip",
        pacman = false,
        git = "/nix/store/khdq35xh67vw7j7k9y4q9l0svxznhx1b-git-2.50.0/bin/git",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    },
    find_package_linux_x86_64_fetch_package_xmake = {
        ["xmake::xorgproto_3a1682704c034fbf87583b32455dde94_release_2023.2_external"] = {
            version = "2023.2",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include"
            }
        },
        ["xmake::libxrandr_a36fcab100ed449e86944351e00c0384_release_1.5.4_external"] = false,
        ["xmake::raylib_efaf75c5cc7042868d9f9fc4196a080a_release_5.5_external"] = false,
        ["xmake::libxcursor_665b359dc13241789f6ca7a586bc5127_release_1.2.3_external"] = false,
        ["xmake::zlib_994fafa590ed48ac9f71516cc846d155_release_v1.3.1_external"] = {
            license = "zlib",
            links = {
                "z"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib/libz.a"
            },
            version = "v1.3.1",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include"
            }
        },
        ["xmake::ca-certificates_e9ba281ae183402a80dc072d4d571c20_release_20250131_external"] = {
            version = "20250131"
        },
        ["xmake::libxau_f406943012fc443596a2e2e23215839d_release_1.0.12_external"] = {
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib/libXau.a"
            },
            links = {
                "Xau"
            },
            version = "1.0.12"
        },
        ["xmake::libxcb_27d2233a10b34ff5971001208db29e2c_release_1.17.0_external"] = {
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-composite.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-damage.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dbe.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dpms.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri2.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri3.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-present.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-glx.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-randr.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-record.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-render.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-res.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-screensaver.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shape.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shm.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-sync.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xevie.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xf86dri.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xfixes.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinerama.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinput.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xkb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xtest.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xv.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xvmc.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-ge.a"
            },
            links = {
                "xcb",
                "xcb-composite",
                "xcb-damage",
                "xcb-dbe",
                "xcb-dpms",
                "xcb-dri2",
                "xcb-dri3",
                "xcb-present",
                "xcb-glx",
                "xcb-randr",
                "xcb-record",
                "xcb-render",
                "xcb-res",
                "xcb-screensaver",
                "xcb-shape",
                "xcb-shm",
                "xcb-sync",
                "xcb-xevie",
                "xcb-xf86dri",
                "xcb-xfixes",
                "xcb-xinerama",
                "xcb-xinput",
                "xcb-xkb",
                "xcb-xtest",
                "xcb-xv",
                "xcb-xvmc",
                "xcb-ge"
            },
            version = "1.17.0"
        },
        ["xmake::openssl_6c51ab6278e2479b883dffafac69fdaf_release_1.1.1-w_external"] = {
            version = "1.1.1-w",
            license = "Apache-2.0",
            links = {
                "ssl",
                "crypto"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libssl.a",
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libcrypto.a"
            },
            syslinks = {
                "pthread",
                "dl"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include"
            }
        },
        ["xmake::util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_1.20.0_external"] = {
            version = "1.20.0"
        },
        ["xmake::libxi_2f225dfd5e274482ae09ba4da0c185c5_release_1.8.2_external"] = false,
        ["xmake::libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_0.5_external"] = {
            version = "0.5"
        },
        ["xmake::libxrender_75df963310db406385383085b95b222e_release_0.9.12_external"] = {
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib/libXrender.a"
            },
            links = {
                "Xrender"
            },
            version = "0.9.12"
        },
        ["xmake::libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_6.0.1_external"] = {
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib/libXfixes.a"
            },
            links = {
                "Xfixes"
            },
            version = "6.0.1"
        },
        ["xmake::libx11_5b3492e889e848b2b511e3f3031c5538_release_1.8.12_external"] = {
            shared = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include"
            },
            links = {
                "X11",
                "X11-xcb"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6.4.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1.0.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1"
            },
            syslinks = "dl",
            version = "1.8.12"
        },
        ["xmake::libxdmcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_1.1.5_external"] = {
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib/libXdmcp.a"
            },
            links = {
                "Xdmcp"
            },
            version = "1.1.5"
        },
        ["xmake::libxext_5a0d83a7724446099fee649c3d9afd32_release_1.3.6_external"] = false,
        ["xmake::xtrans_588d85a9c99148b8ae87dee11b39ab40_release_1.6.0_external"] = {
            version = "1.6.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include"
            }
        },
        ["xmake::xcb-proto_eafb62cb3f37412ebe648eb0ab4b4b35_release_1.17.0_external"] = {
            version = "1.17.0"
        },
        ["xmake::libxinerama_e539e5b3575342a89c7a931295fe40c5_release_1.1.5_external"] = false,
        ["xmake::opengl_4e0143c97b65425b855ad5fd03038b6a_release_latest_external"] = false,
        ["xmake::libffi_46686cf534074f4ebf90e4ab1f9662dd_release_3.4.8_external"] = {
            license = "MIT",
            links = {
                "ffi"
            },
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib/libffi.a"
            },
            version = "3.4.8",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/include"
            }
        }
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    ["core.tools.gcc.has_ldflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0"] = {
            ["--no-allow-shlib-undefined"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--stats"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["-y"] = true,
            ["--check-sections"] = true,
            ["--print-map-discarded"] = true,
            ["--enable-linker-version"] = true,
            ["-plugin-opt"] = true,
            ["-V"] = true,
            ["--pic-executable"] = true,
            ["-Ttext"] = true,
            ["--gc-sections"] = true,
            ["--mri-script"] = true,
            ["-e"] = true,
            ["-Map"] = true,
            ["--no-error-execstack"] = true,
            ["-soname"] = true,
            ["--entry"] = true,
            ["--retain-symbols-file"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["-F"] = true,
            ["--discard-all"] = true,
            ["-debug"] = true,
            ["--default-symver"] = true,
            ["--omagic"] = true,
            ["-z"] = true,
            ["--just-symbols"] = true,
            ["-m"] = true,
            ["--oformat"] = true,
            ["--output"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--strip-discarded"] = true,
            ["--no-rosegment"] = true,
            ["-assert"] = true,
            ["--dynamic-linker"] = true,
            ["--no-map-whole-files"] = true,
            ["-h"] = true,
            ["--gc-keep-exported"] = true,
            ["--warn-execstack"] = true,
            ["--split-by-file"] = true,
            ["--no-warnings"] = true,
            ["-no-pie"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--force-exe-suffix"] = true,
            ["--error-execstack"] = true,
            ["-l"] = true,
            ["-dp"] = true,
            ["--no-check-sections"] = true,
            ["--target-help"] = true,
            ["--no-whole-archive"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["-EB"] = true,
            ["--spare-dynamic-tags"] = true,
            ["-c"] = true,
            ["-Tldata-segment"] = true,
            ["--remap-inputs-file"] = true,
            ["--no-gc-sections"] = true,
            ["--sort-common"] = true,
            ["--whole-archive"] = true,
            ["--force-group-allocation"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--no-warn-execstack"] = true,
            ["--relocatable"] = true,
            ["--no-error-rwx-segments"] = true,
            ["--warn-textrel"] = true,
            ["--version-exports-section"] = true,
            ["-plugin"] = true,
            ["-EL"] = true,
            ["--undefined-version"] = true,
            ["--trace-symbol"] = true,
            ["--no-warn-mismatch"] = true,
            ["--warn-alternate-em"] = true,
            ["--start-group"] = true,
            ["--architecture"] = true,
            ["--strip-debug"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--no-print-gc-sections"] = true,
            ["--allow-multiple-definition"] = true,
            ["--trace"] = true,
            ["-Bsymbolic"] = true,
            ["--orphan-handling"] = true,
            ["-Tdata"] = true,
            ["-nostdlib"] = true,
            ["-Bgroup"] = true,
            ["-Qy"] = true,
            ["--ctf-variables"] = true,
            ["--wrap"] = true,
            ["--no-demangle"] = true,
            ["--default-imported-symver"] = true,
            ["--print-memory-usage"] = true,
            ["--as-needed"] = true,
            ["--discard-none"] = true,
            ["-fini"] = true,
            ["-u"] = true,
            ["--filter"] = true,
            ["--fatal-warnings"] = true,
            ["-A"] = true,
            ["--warn-section-align"] = true,
            ["--pop-state"] = true,
            ["--no-undefined"] = true,
            ["--warn-once"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--no-omagic"] = true,
            ["--cref"] = true,
            ["--warn-execstack-objects"] = true,
            ["-b"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--nmagic"] = true,
            ["--auxiliary"] = true,
            ["--no-ctf-variables"] = true,
            ["-Ur"] = true,
            ["--gpsize"] = true,
            ["--task-link"] = true,
            ["--disable-new-dtags"] = true,
            ["--emit-relocs"] = true,
            ["--dependency-file"] = true,
            ["--no-keep-memory"] = true,
            ["-dT"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--rosegment"] = true,
            ["--verbose"] = true,
            ["--disable-linker-version"] = true,
            ["--export-dynamic"] = true,
            ["-Trodata-segment"] = true,
            ["--sort-section"] = true,
            ["-R"] = true,
            ["-Bno-symbolic"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--unique"] = true,
            ["-Y"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--warn-multiple-gp"] = true,
            ["-plugin-save-temps"] = true,
            ["-rpath-link"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["-a"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--no-undefined-version"] = true,
            ["--help"] = true,
            ["--discard-locals"] = true,
            ["--remap-inputs"] = true,
            ["--warn-common"] = true,
            ["--print-map-locals"] = true,
            ["--split-by-reloc"] = true,
            ["--no-relax"] = true,
            ["--strip-all"] = true,
            ["--library-path"] = true,
            ["--warn-rwx-segments"] = true,
            ["--version"] = true,
            ["--format"] = true,
            ["--reduce-memory-overheads"] = true,
            ["-qmagic"] = true,
            ["-L"] = true,
            ["--undefined"] = true,
            ["--print-sysroot"] = true,
            ["-f"] = true,
            ["--print-map"] = true,
            ["-O"] = true,
            ["--print-gc-sections"] = true,
            ["--no-fatal-warnings"] = true,
            ["--section-ordering-file"] = true,
            ["--out-implib"] = true,
            ["--error-handling-script"] = true,
            ["-G"] = true,
            ["--print-output-format"] = true,
            ["-o"] = true,
            ["--demangle"] = true,
            ["--relax"] = true,
            ["--no-define-common"] = true,
            ["--enable-new-dtags"] = true,
            ["-rpath"] = true,
            ["-g"] = true,
            ["--no-strip-discarded"] = true,
            ["--map-whole-files"] = true,
            ["--traditional-format"] = true,
            ["--require-defined"] = true,
            ["--error-rwx-segments"] = true,
            ["-Bsymbolic-functions"] = true,
            ["-Ttext-segment"] = true,
            ["--library"] = true,
            ["--default-script"] = true,
            ["--end-group"] = true,
            ["--script"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["--eh-frame-hdr"] = true,
            ["--dynamic-list-data"] = true,
            ["--image-base"] = true,
            ["-flto"] = true,
            ["--dynamic-list"] = true,
            ["--no-dynamic-linker"] = true,
            ["-P"] = true,
            ["--no-as-needed"] = true,
            ["-Bshareable"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["-Tbss"] = true,
            ["-I"] = true,
            ["-init"] = true,
            ["--no-print-map-locals"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--no-print-map-discarded"] = true,
            ["--push-state"] = true,
            ["-T"] = true,
            ["--no-export-dynamic"] = true,
            ["--section-start"] = true,
            ["--defsym"] = true,
            ["-static"] = true,
            ["--version-script"] = true
        }
    },
    find_programver_fetch_package_system = {
        ["/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"] = "0.29.2",
        ["/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"] = "3.31.7"
    },
    find_programver = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "14.3.0",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "14.3.0"
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-fdiagnostics-color=always"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-O2"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-MMD -MF"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wextra"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wall"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_ld__-m64 -m64_-fPIC"] = true
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    }
}