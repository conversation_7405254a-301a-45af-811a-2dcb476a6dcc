{
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-O2"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wall"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-fdiagnostics-color=always"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wextra"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_ld__-m64 -m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-MMD -MF"] = true
    },
    find_program = {
        gzip = "/nix/store/31h4wbbp0yvsrciyw46iij6r4c0wc8fp-gzip-1.14/bin/gzip",
        dpkg = false,
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config",
        brew = false,
        nim = false,
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        git = "/nix/store/khdq35xh67vw7j7k9y4q9l0svxznhx1b-git-2.50.0/bin/git",
        emerge = false,
        pacman = false,
        tar = "/nix/store/r8d70l24x06f4fgq246wz2s8zvmj6vxb-gnutar-1.35/bin/tar",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        curl = "/run/current-system/sw/bin/curl"
    },
    find_program_fetch_package_xmake = {
        ["pkg-config"] = false,
        ninja = false,
        cmake = false,
        python3 = false,
        python = false,
        python2 = false
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    },
    find_programver = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "14.3.0",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "14.3.0"
    },
    find_program_fetch_package_system = {
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config",
        cmake = "/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"
    },
    find_package_linux_x86_64_fetch_package_xmake = {
        ["xmake::libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_0.5_external"] = {
            version = "0.5"
        },
        ["xmake::xorgproto_3a1682704c034fbf87583b32455dde94_release_2023.2_external"] = {
            version = "2023.2",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include"
            }
        },
        ["xmake::util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_1.20.0_external"] = {
            version = "1.20.0"
        },
        ["xmake::ca-certificates_e9ba281ae183402a80dc072d4d571c20_release_20250131_external"] = {
            version = "20250131"
        },
        ["xmake::libxext_5a0d83a7724446099fee649c3d9afd32_release_1.3.6_external"] = false,
        ["xmake::libxdmcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_1.1.5_external"] = {
            version = "1.1.5",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib"
            },
            links = {
                "Xdmcp"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib/libXdmcp.a"
            }
        },
        ["xmake::opengl_4e0143c97b65425b855ad5fd03038b6a_release_latest_external"] = false,
        ["xmake::libxrender_75df963310db406385383085b95b222e_release_0.9.12_external"] = {
            version = "0.9.12",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib"
            },
            links = {
                "Xrender"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib/libXrender.a"
            }
        },
        ["xmake::xcb-proto_eafb62cb3f37412ebe648eb0ab4b4b35_release_1.17.0_external"] = {
            version = "1.17.0"
        },
        ["xmake::openssl_6c51ab6278e2479b883dffafac69fdaf_release_1.1.1-w_external"] = {
            version = "1.1.1-w",
            static = true,
            syslinks = {
                "pthread",
                "dl"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib"
            },
            links = {
                "ssl",
                "crypto"
            },
            license = "Apache-2.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libssl.a",
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libcrypto.a"
            }
        },
        ["xmake::libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_6.0.1_external"] = {
            version = "6.0.1",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib"
            },
            links = {
                "Xfixes"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib/libXfixes.a"
            }
        },
        ["xmake::libxi_2f225dfd5e274482ae09ba4da0c185c5_release_1.8.2_external"] = false,
        ["xmake::libxrandr_a36fcab100ed449e86944351e00c0384_release_1.5.4_external"] = false,
        ["xmake::xtrans_588d85a9c99148b8ae87dee11b39ab40_release_1.6.0_external"] = {
            version = "1.6.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include"
            }
        },
        ["xmake::libxinerama_e539e5b3575342a89c7a931295fe40c5_release_1.1.5_external"] = false,
        ["xmake::libx11_5b3492e889e848b2b511e3f3031c5538_release_1.8.12_external"] = {
            version = "1.8.12",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6.4.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1.0.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1"
            },
            syslinks = "dl",
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            },
            shared = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include"
            },
            links = {
                "X11",
                "X11-xcb"
            }
        },
        ["xmake::libxau_f406943012fc443596a2e2e23215839d_release_1.0.12_external"] = {
            version = "1.0.12",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib"
            },
            links = {
                "Xau"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib/libXau.a"
            }
        },
        ["xmake::raylib_efaf75c5cc7042868d9f9fc4196a080a_release_5.5_external"] = false,
        ["xmake::zlib_994fafa590ed48ac9f71516cc846d155_release_v1.3.1_external"] = {
            version = "v1.3.1",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib"
            },
            links = {
                "z"
            },
            license = "zlib",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib/libz.a"
            }
        },
        ["xmake::libffi_46686cf534074f4ebf90e4ab1f9662dd_release_3.4.8_external"] = {
            version = "3.4.8",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib"
            },
            links = {
                "ffi"
            },
            license = "MIT",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib/libffi.a"
            }
        },
        ["xmake::libxcursor_665b359dc13241789f6ca7a586bc5127_release_1.2.3_external"] = false,
        ["xmake::libxcb_27d2233a10b34ff5971001208db29e2c_release_1.17.0_external"] = {
            version = "1.17.0",
            static = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib"
            },
            links = {
                "xcb",
                "xcb-composite",
                "xcb-damage",
                "xcb-dbe",
                "xcb-dpms",
                "xcb-dri2",
                "xcb-dri3",
                "xcb-present",
                "xcb-glx",
                "xcb-randr",
                "xcb-record",
                "xcb-render",
                "xcb-res",
                "xcb-screensaver",
                "xcb-shape",
                "xcb-shm",
                "xcb-sync",
                "xcb-xevie",
                "xcb-xf86dri",
                "xcb-xfixes",
                "xcb-xinerama",
                "xcb-xinput",
                "xcb-xkb",
                "xcb-xtest",
                "xcb-xv",
                "xcb-xvmc",
                "xcb-ge"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-composite.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-damage.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dbe.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dpms.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri2.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri3.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-present.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-glx.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-randr.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-record.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-render.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-res.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-screensaver.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shape.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shm.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-sync.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xevie.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xf86dri.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xfixes.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinerama.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinput.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xkb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xtest.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xv.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xvmc.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-ge.a"
            }
        }
    },
    find_package_linux_x86_64_fetch_package_system = {
        opengl_4e0143c97b65425b855ad5fd03038b6a_release_external = {
            version = "4.5",
            libfiles = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
            },
            sysincludedirs = {
                "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
            },
            shared = true,
            links = {
                "OpenGL"
            },
            linkdirs = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
            }
        },
        libxi_2f225dfd5e274482ae09ba4da0c185c5_release_external = {
            version = "1.8.2",
            libfiles = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include"
            },
            shared = true,
            links = {
                "Xi"
            },
            linkdirs = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib"
            }
        },
        ["apt::libxcursor-dev_665b359dc13241789f6ca7a586bc5127_release_external"] = false,
        libxrandr_a36fcab100ed449e86944351e00c0384_release_external = {
            version = "1.5.4",
            libfiles = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include"
            },
            shared = true,
            links = {
                "Xrandr"
            },
            linkdirs = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib"
            }
        },
        ["apt::libxi-dev_2f225dfd5e274482ae09ba4da0c185c5_release_external"] = false,
        ["apt::libxrandr-dev_a36fcab100ed449e86944351e00c0384_release_external"] = false,
        ["apt::libxext-dev_5a0d83a7724446099fee649c3d9afd32_release_external"] = false,
        libxcursor_665b359dc13241789f6ca7a586bc5127_release_external = {
            version = "1.2.3",
            libfiles = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include"
            },
            shared = true,
            links = {
                "Xcursor"
            },
            linkdirs = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib"
            }
        },
        raylib_efaf75c5cc7042868d9f9fc4196a080a_release_external = {
            version = "5.5.0",
            libfiles = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib/libraylib.so"
            },
            sysincludedirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/include"
            },
            shared = true,
            links = {
                "raylib"
            },
            linkdirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib"
            }
        },
        libxext_5a0d83a7724446099fee649c3d9afd32_release_external = {
            version = "1.3.6",
            libfiles = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include"
            },
            shared = true,
            links = {
                "Xext"
            },
            linkdirs = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib"
            }
        },
        ["apt::libxinerama-dev_e539e5b3575342a89c7a931295fe40c5_release_external"] = false,
        libxinerama_e539e5b3575342a89c7a931295fe40c5_release_external = {
            version = "1.1.5",
            libfiles = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include"
            },
            shared = true,
            links = {
                "Xinerama"
            },
            linkdirs = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib"
            }
        }
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    find_program_envs_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    find_programver_fetch_package_system = {
        ["/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"] = "3.31.7",
        ["/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"] = "0.29.2"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0"] = {
            ["-Xpreprocessor"] = true,
            ["--param"] = true,
            ["-save-temps"] = true,
            ["-print-multiarch"] = true,
            ["-o"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-dumpversion"] = true,
            ["-B"] = true,
            ["-time"] = true,
            ["-Xlinker"] = true,
            ["-pass-exit-codes"] = true,
            ["--help"] = true,
            ["-c"] = true,
            ["-print-search-dirs"] = true,
            ["-shared"] = true,
            ["-dumpspecs"] = true,
            ["-print-multi-directory"] = true,
            ["-pipe"] = true,
            ["-pie"] = true,
            ["-E"] = true,
            ["-v"] = true,
            ["-S"] = true,
            ["-x"] = true,
            ["--target-help"] = true,
            ["-print-multi-lib"] = true,
            ["--version"] = true,
            ["-print-multi-os-directory"] = true,
            ["-print-sysroot"] = true,
            ["-dumpmachine"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-Xassembler"] = true
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0"] = {
            ["--warn-textrel"] = true,
            ["--no-strip-discarded"] = true,
            ["--export-dynamic-symbol"] = true,
            ["-P"] = true,
            ["--version"] = true,
            ["--mri-script"] = true,
            ["--emit-relocs"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["--demangle"] = true,
            ["-a"] = true,
            ["--script"] = true,
            ["--default-script"] = true,
            ["--omagic"] = true,
            ["--just-symbols"] = true,
            ["--warn-execstack-objects"] = true,
            ["--dependency-file"] = true,
            ["-y"] = true,
            ["--no-error-execstack"] = true,
            ["--no-demangle"] = true,
            ["--discard-none"] = true,
            ["--disable-linker-version"] = true,
            ["-EB"] = true,
            ["--verbose"] = true,
            ["--end-group"] = true,
            ["--entry"] = true,
            ["-Bno-symbolic"] = true,
            ["--split-by-file"] = true,
            ["-A"] = true,
            ["-Tldata-segment"] = true,
            ["--strip-discarded"] = true,
            ["-h"] = true,
            ["--enable-linker-version"] = true,
            ["--nmagic"] = true,
            ["--force-group-allocation"] = true,
            ["--no-check-sections"] = true,
            ["--no-warn-mismatch"] = true,
            ["--warn-once"] = true,
            ["-Ttext"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--no-as-needed"] = true,
            ["-debug"] = true,
            ["-z"] = true,
            ["--library"] = true,
            ["--print-memory-usage"] = true,
            ["--whole-archive"] = true,
            ["--sort-section"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--warn-alternate-em"] = true,
            ["--output"] = true,
            ["--pic-executable"] = true,
            ["-qmagic"] = true,
            ["-c"] = true,
            ["--stats"] = true,
            ["--no-print-map-locals"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--oformat"] = true,
            ["--dynamic-linker"] = true,
            ["--rosegment"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--start-group"] = true,
            ["--print-gc-sections"] = true,
            ["-no-pie"] = true,
            ["--warn-section-align"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--warn-multiple-gp"] = true,
            ["--orphan-handling"] = true,
            ["--print-sysroot"] = true,
            ["--no-omagic"] = true,
            ["--gc-keep-exported"] = true,
            ["--cref"] = true,
            ["-nostdlib"] = true,
            ["--no-gc-sections"] = true,
            ["-Map"] = true,
            ["--no-warn-execstack"] = true,
            ["-m"] = true,
            ["--section-start"] = true,
            ["--wrap"] = true,
            ["--dynamic-list-data"] = true,
            ["--no-ctf-variables"] = true,
            ["-plugin-save-temps"] = true,
            ["--section-ordering-file"] = true,
            ["--check-sections"] = true,
            ["--fatal-warnings"] = true,
            ["--pop-state"] = true,
            ["--strip-debug"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--print-map"] = true,
            ["--no-undefined"] = true,
            ["-assert"] = true,
            ["-I"] = true,
            ["-T"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["-fini"] = true,
            ["--push-state"] = true,
            ["--warn-execstack"] = true,
            ["--warn-common"] = true,
            ["--help"] = true,
            ["--gc-sections"] = true,
            ["--remap-inputs"] = true,
            ["-Bsymbolic-functions"] = true,
            ["-Qy"] = true,
            ["--error-execstack"] = true,
            ["-u"] = true,
            ["--discard-all"] = true,
            ["--no-define-common"] = true,
            ["--architecture"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--undefined-version"] = true,
            ["-soname"] = true,
            ["--print-map-locals"] = true,
            ["--disable-new-dtags"] = true,
            ["--sort-common"] = true,
            ["-L"] = true,
            ["--no-error-rwx-segments"] = true,
            ["-Ur"] = true,
            ["-EL"] = true,
            ["--no-warnings"] = true,
            ["-Bshareable"] = true,
            ["--traditional-format"] = true,
            ["--as-needed"] = true,
            ["--reduce-memory-overheads"] = true,
            ["-dT"] = true,
            ["--version-script"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--retain-symbols-file"] = true,
            ["-static"] = true,
            ["--format"] = true,
            ["--map-whole-files"] = true,
            ["-init"] = true,
            ["-dp"] = true,
            ["--relocatable"] = true,
            ["--warn-rwx-segments"] = true,
            ["-rpath-link"] = true,
            ["--default-imported-symver"] = true,
            ["-o"] = true,
            ["--error-unresolved-symbols"] = true,
            ["-l"] = true,
            ["-Ttext-segment"] = true,
            ["--print-output-format"] = true,
            ["--trace"] = true,
            ["-flto"] = true,
            ["--gpsize"] = true,
            ["--allow-multiple-definition"] = true,
            ["--dynamic-list"] = true,
            ["--print-map-discarded"] = true,
            ["-F"] = true,
            ["--out-implib"] = true,
            ["--no-dynamic-linker"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["-Trodata-segment"] = true,
            ["--export-dynamic"] = true,
            ["--force-exe-suffix"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["-g"] = true,
            ["-plugin"] = true,
            ["-Y"] = true,
            ["-rpath"] = true,
            ["-V"] = true,
            ["--filter"] = true,
            ["-Bsymbolic"] = true,
            ["--defsym"] = true,
            ["--ctf-variables"] = true,
            ["--task-link"] = true,
            ["--no-keep-memory"] = true,
            ["--enable-new-dtags"] = true,
            ["--no-undefined-version"] = true,
            ["--eh-frame-hdr"] = true,
            ["--no-map-whole-files"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--strip-all"] = true,
            ["--discard-locals"] = true,
            ["--no-print-map-discarded"] = true,
            ["--undefined"] = true,
            ["--auxiliary"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--library-path"] = true,
            ["--default-symver"] = true,
            ["-Tdata"] = true,
            ["--no-rosegment"] = true,
            ["--split-by-reloc"] = true,
            ["-G"] = true,
            ["--relax"] = true,
            ["-O"] = true,
            ["--no-relax"] = true,
            ["-b"] = true,
            ["-f"] = true,
            ["--image-base"] = true,
            ["--trace-symbol"] = true,
            ["--remap-inputs-file"] = true,
            ["--version-exports-section"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--unique"] = true,
            ["--no-whole-archive"] = true,
            ["-e"] = true,
            ["--error-handling-script"] = true,
            ["--no-print-gc-sections"] = true,
            ["--error-rwx-segments"] = true,
            ["-Tbss"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--target-help"] = true,
            ["--require-defined"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["-Bgroup"] = true,
            ["--spare-dynamic-tags"] = true,
            ["-R"] = true,
            ["-plugin-opt"] = true,
            ["--no-fatal-warnings"] = true,
            ["--no-export-dynamic"] = true
        }
    }
}