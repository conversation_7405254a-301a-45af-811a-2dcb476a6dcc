{
    find_program = {
        gzip = "/nix/store/31h4wbbp0yvsrciyw46iij6r4c0wc8fp-gzip-1.14/bin/gzip",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        nim = false,
        emerge = false,
        brew = false,
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        pacman = false,
        tar = "/nix/store/r8d70l24x06f4fgq246wz2s8zvmj6vxb-gnutar-1.35/bin/tar",
        git = "/nix/store/khdq35xh67vw7j7k9y4q9l0svxznhx1b-git-2.50.0/bin/git",
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        dpkg = false,
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"
    },
    find_package_linux_x86_64_fetch_package_system = {
        ["apt::libxext-dev_5a0d83a7724446099fee649c3d9afd32_release_external"] = false,
        libxi_2f225dfd5e274482ae09ba4da0c185c5_release_external = {
            shared = true,
            version = "1.8.2",
            links = {
                "Xi"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include"
            },
            linkdirs = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib"
            },
            libfiles = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so"
            }
        },
        libxinerama_e539e5b3575342a89c7a931295fe40c5_release_external = {
            shared = true,
            version = "1.1.5",
            links = {
                "Xinerama"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include"
            },
            linkdirs = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib"
            },
            libfiles = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so"
            }
        },
        ["apt::libxrandr-dev_a36fcab100ed449e86944351e00c0384_release_external"] = false,
        libxcursor_665b359dc13241789f6ca7a586bc5127_release_external = {
            shared = true,
            version = "1.2.3",
            links = {
                "Xcursor"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include"
            },
            linkdirs = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib"
            },
            libfiles = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so"
            }
        },
        ["apt::libxcursor-dev_665b359dc13241789f6ca7a586bc5127_release_external"] = false,
        raylib_efaf75c5cc7042868d9f9fc4196a080a_release_external = {
            shared = true,
            version = "5.5.0",
            links = {
                "raylib"
            },
            sysincludedirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/include"
            },
            linkdirs = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib"
            },
            libfiles = {
                "/nix/store/f4x7z33ymmqn0m0pvi5ydxifsfmic1jw-raylib-5.5/lib/libraylib.so"
            }
        },
        opengl_4e0143c97b65425b855ad5fd03038b6a_release_external = {
            shared = true,
            version = "4.5",
            links = {
                "OpenGL"
            },
            sysincludedirs = {
                "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
            },
            linkdirs = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
            },
            libfiles = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
            }
        },
        ["apt::libxinerama-dev_e539e5b3575342a89c7a931295fe40c5_release_external"] = false,
        libxrandr_a36fcab100ed449e86944351e00c0384_release_external = {
            shared = true,
            version = "1.5.4",
            links = {
                "Xrandr"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include"
            },
            linkdirs = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib"
            },
            libfiles = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so"
            }
        },
        libxext_5a0d83a7724446099fee649c3d9afd32_release_external = {
            shared = true,
            version = "1.3.6",
            links = {
                "Xext"
            },
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include"
            },
            linkdirs = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib"
            },
            libfiles = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so"
            }
        },
        ["apt::libxi-dev_2f225dfd5e274482ae09ba4da0c185c5_release_external"] = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0"] = {
            ["--no-undefined-version"] = true,
            ["--as-needed"] = true,
            ["--verbose"] = true,
            ["-dp"] = true,
            ["--print-map-discarded"] = true,
            ["-F"] = true,
            ["-nostdlib"] = true,
            ["--enable-linker-version"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--dependency-file"] = true,
            ["--auxiliary"] = true,
            ["--traditional-format"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["-e"] = true,
            ["-u"] = true,
            ["--script"] = true,
            ["--no-demangle"] = true,
            ["--strip-debug"] = true,
            ["-m"] = true,
            ["-Bshareable"] = true,
            ["--print-map"] = true,
            ["--mri-script"] = true,
            ["--sort-common"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["-no-pie"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["--retain-symbols-file"] = true,
            ["-Map"] = true,
            ["--allow-multiple-definition"] = true,
            ["--enable-new-dtags"] = true,
            ["-Trodata-segment"] = true,
            ["-G"] = true,
            ["--no-rosegment"] = true,
            ["--ctf-variables"] = true,
            ["-O"] = true,
            ["--no-print-map-discarded"] = true,
            ["-dT"] = true,
            ["-assert"] = true,
            ["--demangle"] = true,
            ["--start-group"] = true,
            ["--no-undefined"] = true,
            ["--nmagic"] = true,
            ["--pop-state"] = true,
            ["--no-warn-execstack"] = true,
            ["--push-state"] = true,
            ["-z"] = true,
            ["-g"] = true,
            ["--disable-new-dtags"] = true,
            ["-init"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--default-script"] = true,
            ["--no-export-dynamic"] = true,
            ["--dynamic-list-data"] = true,
            ["-Ttext-segment"] = true,
            ["-Tldata-segment"] = true,
            ["-R"] = true,
            ["--trace"] = true,
            ["--filter"] = true,
            ["-A"] = true,
            ["--warn-alternate-em"] = true,
            ["--warn-section-align"] = true,
            ["--print-map-locals"] = true,
            ["--wrap"] = true,
            ["--print-memory-usage"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--warn-once"] = true,
            ["--remap-inputs"] = true,
            ["--gc-keep-exported"] = true,
            ["-Ttext"] = true,
            ["--discard-none"] = true,
            ["--no-error-rwx-segments"] = true,
            ["--section-start"] = true,
            ["--section-ordering-file"] = true,
            ["--force-group-allocation"] = true,
            ["--warn-textrel"] = true,
            ["--default-symver"] = true,
            ["--no-ctf-variables"] = true,
            ["--fatal-warnings"] = true,
            ["--check-sections"] = true,
            ["-Bno-symbolic"] = true,
            ["--defsym"] = true,
            ["--entry"] = true,
            ["--remap-inputs-file"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["-I"] = true,
            ["--task-link"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--warn-execstack"] = true,
            ["--end-group"] = true,
            ["--warn-common"] = true,
            ["--help"] = true,
            ["--map-whole-files"] = true,
            ["--no-error-execstack"] = true,
            ["--error-execstack"] = true,
            ["--discard-all"] = true,
            ["--no-define-common"] = true,
            ["--default-imported-symver"] = true,
            ["-a"] = true,
            ["-soname"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["-P"] = true,
            ["-Ur"] = true,
            ["--no-as-needed"] = true,
            ["-o"] = true,
            ["--no-fatal-warnings"] = true,
            ["--relax"] = true,
            ["--warn-execstack-objects"] = true,
            ["--no-gc-sections"] = true,
            ["--trace-symbol"] = true,
            ["--warn-rwx-segments"] = true,
            ["--unique"] = true,
            ["--gpsize"] = true,
            ["--dynamic-list"] = true,
            ["--error-rwx-segments"] = true,
            ["--no-print-map-locals"] = true,
            ["--out-implib"] = true,
            ["--no-map-whole-files"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--force-exe-suffix"] = true,
            ["-L"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--cref"] = true,
            ["--no-dynamic-linker"] = true,
            ["-Y"] = true,
            ["--omagic"] = true,
            ["-T"] = true,
            ["--gc-sections"] = true,
            ["--export-dynamic"] = true,
            ["--output"] = true,
            ["--disable-linker-version"] = true,
            ["--dynamic-linker"] = true,
            ["--rosegment"] = true,
            ["-EB"] = true,
            ["--architecture"] = true,
            ["--strip-all"] = true,
            ["-y"] = true,
            ["-plugin"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--no-strip-discarded"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--version-script"] = true,
            ["--undefined"] = true,
            ["-rpath-link"] = true,
            ["-Tdata"] = true,
            ["-debug"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--no-check-sections"] = true,
            ["--undefined-version"] = true,
            ["--reduce-memory-overheads"] = true,
            ["-b"] = true,
            ["--image-base"] = true,
            ["-static"] = true,
            ["-EL"] = true,
            ["--discard-locals"] = true,
            ["-plugin-save-temps"] = true,
            ["--whole-archive"] = true,
            ["--require-defined"] = true,
            ["-h"] = true,
            ["--library"] = true,
            ["--version"] = true,
            ["-c"] = true,
            ["--no-print-gc-sections"] = true,
            ["--orphan-handling"] = true,
            ["--no-warnings"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--eh-frame-hdr"] = true,
            ["--print-output-format"] = true,
            ["--stats"] = true,
            ["-qmagic"] = true,
            ["-plugin-opt"] = true,
            ["-fini"] = true,
            ["--no-relax"] = true,
            ["-rpath"] = true,
            ["--print-sysroot"] = true,
            ["-Bgroup"] = true,
            ["--split-by-file"] = true,
            ["--no-warn-mismatch"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["-Bsymbolic"] = true,
            ["--print-gc-sections"] = true,
            ["--no-omagic"] = true,
            ["-flto"] = true,
            ["--sort-section"] = true,
            ["--library-path"] = true,
            ["--no-keep-memory"] = true,
            ["--warn-multiple-gp"] = true,
            ["--format"] = true,
            ["--oformat"] = true,
            ["-Tbss"] = true,
            ["-V"] = true,
            ["--error-handling-script"] = true,
            ["--pic-executable"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["-f"] = true,
            ["--strip-discarded"] = true,
            ["--no-whole-archive"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--just-symbols"] = true,
            ["--relocatable"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--split-by-reloc"] = true,
            ["--target-help"] = true,
            ["-Qy"] = true,
            ["--version-exports-section"] = true,
            ["-l"] = true,
            ["--emit-relocs"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-O2"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wextra"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_ld__-m64 -m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cflags_-m64_-Wall"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-MMD -MF"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-fdiagnostics-color=always"] = true
    },
    find_package_linux_x86_64_fetch_package_xmake = {
        ["xmake::ca-certificates_e9ba281ae183402a80dc072d4d571c20_release_20250131_external"] = {
            version = "20250131"
        },
        ["xmake::libxinerama_e539e5b3575342a89c7a931295fe40c5_release_1.1.5_external"] = false,
        ["xmake::libxext_5a0d83a7724446099fee649c3d9afd32_release_1.3.6_external"] = false,
        ["xmake::xorgproto_3a1682704c034fbf87583b32455dde94_release_2023.2_external"] = {
            version = "2023.2",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include"
            }
        },
        ["xmake::raylib_efaf75c5cc7042868d9f9fc4196a080a_release_5.5_external"] = false,
        ["xmake::libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_0.5_external"] = {
            version = "0.5"
        },
        ["xmake::libx11_5b3492e889e848b2b511e3f3031c5538_release_1.8.12_external"] = {
            shared = true,
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            },
            version = "1.8.12",
            syslinks = "dl",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include"
            },
            links = {
                "X11",
                "X11-xcb"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6.4.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11.so.6",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1.0.0",
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib/libX11-xcb.so.1"
            }
        },
        ["xmake::opengl_4e0143c97b65425b855ad5fd03038b6a_release_latest_external"] = false,
        ["xmake::openssl_6c51ab6278e2479b883dffafac69fdaf_release_1.1.1-w_external"] = {
            syslinks = {
                "pthread",
                "dl"
            },
            license = "Apache-2.0",
            linkdirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib"
            },
            version = "1.1.1-w",
            static = true,
            links = {
                "ssl",
                "crypto"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libssl.a",
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libcrypto.a"
            }
        },
        ["xmake::libxrandr_a36fcab100ed449e86944351e00c0384_release_1.5.4_external"] = false,
        ["xmake::xcb-proto_eafb62cb3f37412ebe648eb0ab4b4b35_release_1.17.0_external"] = {
            version = "1.17.0"
        },
        ["xmake::util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_1.20.0_external"] = {
            version = "1.20.0"
        },
        ["xmake::libxcb_27d2233a10b34ff5971001208db29e2c_release_1.17.0_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib"
            },
            links = {
                "xcb",
                "xcb-composite",
                "xcb-damage",
                "xcb-dbe",
                "xcb-dpms",
                "xcb-dri2",
                "xcb-dri3",
                "xcb-present",
                "xcb-glx",
                "xcb-randr",
                "xcb-record",
                "xcb-render",
                "xcb-res",
                "xcb-screensaver",
                "xcb-shape",
                "xcb-shm",
                "xcb-sync",
                "xcb-xevie",
                "xcb-xf86dri",
                "xcb-xfixes",
                "xcb-xinerama",
                "xcb-xinput",
                "xcb-xkb",
                "xcb-xtest",
                "xcb-xv",
                "xcb-xvmc",
                "xcb-ge"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include"
            },
            version = "1.17.0",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-composite.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-damage.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dbe.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dpms.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri2.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-dri3.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-present.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-glx.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-randr.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-record.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-render.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-res.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-screensaver.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shape.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-shm.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-sync.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xevie.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xf86dri.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xfixes.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinerama.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xinput.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xkb.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xtest.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xv.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-xvmc.a",
                "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/lib/libxcb-ge.a"
            }
        },
        ["xmake::libxi_2f225dfd5e274482ae09ba4da0c185c5_release_1.8.2_external"] = false,
        ["xmake::xtrans_588d85a9c99148b8ae87dee11b39ab40_release_1.6.0_external"] = {
            version = "1.6.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include"
            }
        },
        ["xmake::libxrender_75df963310db406385383085b95b222e_release_0.9.12_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib"
            },
            links = {
                "Xrender"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include"
            },
            version = "0.9.12",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/lib/libXrender.a"
            }
        },
        ["xmake::libxau_f406943012fc443596a2e2e23215839d_release_1.0.12_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib"
            },
            links = {
                "Xau"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include"
            },
            version = "1.0.12",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/lib/libXau.a"
            }
        },
        ["xmake::libffi_46686cf534074f4ebf90e4ab1f9662dd_release_3.4.8_external"] = {
            license = "MIT",
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib"
            },
            links = {
                "ffi"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/include"
            },
            version = "3.4.8",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib/libffi.a"
            }
        },
        ["xmake::libxcursor_665b359dc13241789f6ca7a586bc5127_release_1.2.3_external"] = false,
        ["xmake::libxdmcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_1.1.5_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib"
            },
            links = {
                "Xdmcp"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include"
            },
            version = "1.1.5",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/lib/libXdmcp.a"
            }
        },
        ["xmake::libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_6.0.1_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib"
            },
            links = {
                "Xfixes"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include"
            },
            version = "6.0.1",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/lib/libXfixes.a"
            }
        },
        ["xmake::zlib_994fafa590ed48ac9f71516cc846d155_release_v1.3.1_external"] = {
            license = "zlib",
            linkdirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib"
            },
            links = {
                "z"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include"
            },
            version = "v1.3.1",
            libfiles = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib/libz.a"
            }
        }
    },
    find_programver = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "14.3.0",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "14.3.0"
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    },
    find_program_fetch_package_system = {
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config",
        cmake = "/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"
    },
    find_program_fetch_package_xmake = {
        cmake = false,
        python2 = false,
        python = false,
        python3 = false,
        ["pkg-config"] = false,
        ninja = false
    },
    find_programver_fetch_package_system = {
        ["/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"] = "0.29.2",
        ["/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"] = "3.31.7"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0"] = {
            ["-Xassembler"] = true,
            ["-pass-exit-codes"] = true,
            ["-dumpspecs"] = true,
            ["-shared"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-Xpreprocessor"] = true,
            ["-o"] = true,
            ["-dumpversion"] = true,
            ["-print-search-dirs"] = true,
            ["--param"] = true,
            ["-pipe"] = true,
            ["-E"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-S"] = true,
            ["-print-multi-directory"] = true,
            ["-print-multiarch"] = true,
            ["--version"] = true,
            ["-c"] = true,
            ["-pie"] = true,
            ["-B"] = true,
            ["-print-multi-os-directory"] = true,
            ["-x"] = true,
            ["-v"] = true,
            ["-Xlinker"] = true,
            ["-print-multi-lib"] = true,
            ["-print-sysroot"] = true,
            ["-dumpmachine"] = true,
            ["--target-help"] = true,
            ["--help"] = true,
            ["-save-temps"] = true,
            ["-time"] = true,
            ["-print-sysroot-headers-suffix"] = true
        }
    },
    ["detect.sdks.find_vcpkgdir"] = false
}