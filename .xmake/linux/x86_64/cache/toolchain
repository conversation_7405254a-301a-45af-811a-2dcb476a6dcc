{
    envs_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    cross_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = false,
        __global = true,
        plat = "linux"
    },
    gfortran_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    swift_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    gcc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = {
            program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
            name = "gcc"
        },
        __global = true,
        plat = "linux"
    },
    fasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    go_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    nim_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = false,
        __global = true,
        plat = "linux"
    },
    cuda_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    fpc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    yasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    nasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    ["tool_target_controller-synth_linux_x86_64_ld"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolchain_info = {
            arch = "x86_64",
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux"
        },
        toolname = "gxx"
    },
    rust_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __checked = true,
        __global = true,
        plat = "linux"
    },
    ["tool_target_controller-synth_linux_x86_64_cc"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        toolchain_info = {
            arch = "x86_64",
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux"
        },
        toolname = "gcc"
    }
}