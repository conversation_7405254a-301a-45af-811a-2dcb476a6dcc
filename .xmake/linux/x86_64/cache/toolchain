{
    gcc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = {
            program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
            name = "gcc"
        }
    },
    nim_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = false
    },
    go_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    fasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    ["tool_target_controller-synth_linux_x86_64_cc"] = {
        toolname = "gcc",
        toolchain_info = {
            arch = "x86_64",
            name = "envs",
            plat = "linux",
            cachekey = "envs_arch_x86_64_plat_linux"
        },
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    rust_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    envs_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    yasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    fpc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    swift_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    cross_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = false
    },
    nasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    gfortran_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    },
    ["tool_target_controller-synth_linux_x86_64_ld"] = {
        toolname = "gxx",
        toolchain_info = {
            arch = "x86_64",
            name = "envs",
            plat = "linux",
            cachekey = "envs_arch_x86_64_plat_linux"
        },
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    },
    cuda_arch_x86_64_plat_linux = {
        arch = "x86_64",
        __global = true,
        plat = "linux",
        __checked = true
    }
}