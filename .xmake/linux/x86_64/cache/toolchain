{
    ["tool_target_controller-synth_linux_x86_64_ld"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolchain_info = {
            plat = "linux",
            cachekey = "envs_arch_x86_64_plat_linux",
            name = "envs",
            arch = "x86_64"
        },
        toolname = "gxx"
    },
    cross_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = false
    },
    nim_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = false
    },
    cuda_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    nasm_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    rust_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    gfortran_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    swift_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    envs_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    fpc_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    ["tool_target_controller-synth_linux_x86_64_cc"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        toolchain_info = {
            plat = "linux",
            cachekey = "envs_arch_x86_64_plat_linux",
            name = "envs",
            arch = "x86_64"
        },
        toolname = "gcc"
    },
    gcc_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = {
            program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
            name = "gcc"
        }
    },
    yasm_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    fasm_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    },
    go_arch_x86_64_plat_linux = {
        plat = "linux",
        arch = "x86_64",
        __global = true,
        __checked = true
    }
}